from .utils.face_recognition_algo import FaceRecognitionAlgo
import json
import os


def load():
    """
    加载人脸识别算法
    
    Returns:
        算法实例字典
    """
    try:
        # 读取配置文件
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config/documentation.json')
        with open(config_path, 'r', encoding='utf-8') as file:
            docu = json.load(file)
        
        model_name = docu["modelName"]
        model_version = docu["modelVersion"]
        algo_name = docu["algoSet"][0]["algoName"]
        
        # 创建算法实例
        algo_instance = FaceRecognitionAlgo(model_name, model_version, algo_name)
        
        # 返回算法实例字典
        algo_key = f"{model_name}:{model_version}:{algo_name}"
        
        return {algo_key: algo_instance}
        
    except Exception as e:
        print(f"加载人脸识别算法失败: {e}")
        return {}


if __name__ == "__main__":
    # 测试加载
    algorithms = load()
    if algorithms:
        print("人脸识别算法加载成功:")
        for key in algorithms.keys():
            print(f"  - {key}")
    else:
        print("人脸识别算法加载失败")
