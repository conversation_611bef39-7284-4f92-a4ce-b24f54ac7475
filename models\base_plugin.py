#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法插件基类
提供标准的算法接口定义
"""

from abc import ABC, abstractmethod


class BasePlugin(ABC):
    """
    算法插件基类
    所有算法插件都应该继承此类并实现相应的方法
    """
    
    def __init__(self, model_name, model_version, algo_name):
        """
        初始化算法插件
        
        Args:
            model_name: 模型名称
            model_version: 模型版本
            algo_name: 算法名称
        """
        self.model_name = model_name
        self.model_version = model_version
        self.algo_name = algo_name
        
    @abstractmethod
    def infer(self, data, model_way):
        """
        推理接口
        
        Args:
            data: 输入数据字典
            model_way: 模型路径
            
        Returns:
            bool: 推理是否成功
        """
        pass
        
    @abstractmethod
    def start_task(self, task_id, data):
        """
        启动流式检测任务
        
        Args:
            task_id: 任务ID
            data: 任务配置数据
            
        Returns:
            int: 状态码 (200成功, 500失败)
        """
        pass
        
    @abstractmethod
    def stop_task(self, task_id):
        """
        停止流式检测任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            int: 状态码 (200成功, 500失败)
        """
        pass
        
    @abstractmethod
    def query_task(self, task_id=None):
        """
        查询任务状态
        
        Args:
            task_id: 任务ID，为None时查询所有任务
            
        Returns:
            dict: 任务状态字典
        """
        pass
        
    def change_language(self, language):
        """
        切换语言
        
        Args:
            language: 语言代码
            
        Returns:
            dict: 切换结果
        """
        return {"status": "success", "language": language}
        
    def get_model_info(self):
        """
        获取模型信息
        
        Returns:
            dict: 模型信息
        """
        return {
            "model_name": self.model_name,
            "model_version": self.model_version,
            "algo_name": self.algo_name
        }
