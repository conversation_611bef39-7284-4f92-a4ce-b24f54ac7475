import cv2
import numpy as np
import base64
import time
from typing import Dict, List, Any, Optional, Tuple
from ..utils.log import mylog
from ..utils.i18n import I18n


class PostProcess:
    """后处理类"""
    
    def __init__(self, i18n: I18n, post_mess: List[str]):
        """
        初始化后处理器
        
        Args:
            i18n: 国际化对象
            post_mess: 后处理消息列表 [model_name, model_version, algo_name]
        """
        self.i18n = i18n
        self.post_mess = post_mess
        self.model_name, self.model_version, self.algo_name = post_mess
        mylog.info('加载人脸识别后处理模块')
    
    def run_process(self, data: Dict[str, Any], recognition_result: Dict[str, Any], source_img: np.ndarray) -> Tuple[Optional[Dict], bool]:
        """
        运行后处理流程
        
        Args:
            data: 原始请求数据
            recognition_result: 人脸识别结果
            source_img: 原始图像
            
        Returns:
            处理后的结果字典和是否成功标志
        """
        try:
            if recognition_result is None or recognition_result.get("status") == "error":
                return None, False
            
            business_data = data.get("businessData", {})
            advset_value = business_data.get("advsetValue", {})
            recv_data_types = advset_value.get("recvDataType", ["infer_results"])
            recognition_mode = advset_value.get("recognitionMode", "known_face")
            
            # 构建基础结果
            result = {
                "modelName": self.model_name,
                "modelVersion": self.model_version,
                "algoName": self.algo_name,
                "timestamp": int(time.time() * 1000),
                "status": recognition_result.get("status", "success"),
                "message": recognition_result.get("message", ""),
                "faceCount": len(recognition_result.get("faces", []))
            }
            
            # 处理人脸信息
            faces_info = self._process_faces(recognition_result.get("faces", []), recognition_mode)
            
            # 根据请求的数据类型添加相应内容
            if "infer_results" in recv_data_types:
                result["inferResults"] = faces_info
            
            if "face_info" in recv_data_types:
                result["faceInfo"] = self._format_face_info(faces_info)
            
            if "draw_image" in recv_data_types:
                drawn_image = self._draw_faces_on_image(source_img, recognition_result.get("faces", []))
                if drawn_image is not None:
                    result["drawImage"] = self._image_to_base64(drawn_image)
            
            if "source_image" in recv_data_types:
                result["sourceImage"] = self._image_to_base64(source_img)
            
            # 判断是否有有效检测结果
            has_valid_result = self._has_valid_detection(faces_info, recognition_mode)
            
            return result, has_valid_result
            
        except Exception as e:
            mylog.error(f"后处理失败: {e}")
            return None, False
    
    def _process_faces(self, faces: List[Dict], recognition_mode: str) -> List[Dict]:
        """
        处理人脸信息 - 修复报警逻辑

        Args:
            faces: 人脸列表
            recognition_mode: 识别模式

        Returns:
            处理后的人脸信息列表
        """
        processed_faces = []

        for face in faces:
            face_info = {
                "bbox": face.get("bbox", []),
                "confidence": face.get("confidence", 0.0),
                "landmarks": face.get("landmarks", []),
                "status": face.get("status", "unknown")
            }

            # 根据识别模式处理结果
            if recognition_mode == "known_face":
                # 已知人脸模式：只返回已知人脸
                if face.get("status") == "known":
                    face_info.update({
                        "personId": face.get("person_id", ""),
                        "name": face.get("name", ""),
                        "similarity": face.get("similarity", 0.0),
                        "faceId": face.get("face_id", "")
                    })
                    processed_faces.append(face_info)

            elif recognition_mode == "stranger_detection":
                # 陌生人检测模式：只返回陌生人
                if face.get("status") == "stranger":
                    face_info.update({
                        "similarity": face.get("similarity", 0.0)
                    })
                    processed_faces.append(face_info)

            elif recognition_mode == "all":
                # 全部模式：返回所有检测到的人脸
                if face.get("status") == "known":
                    face_info.update({
                        "personId": face.get("person_id", ""),
                        "name": face.get("name", ""),
                        "similarity": face.get("similarity", 0.0),
                        "faceId": face.get("face_id", "")
                    })
                elif face.get("status") == "stranger":
                    face_info.update({
                        "similarity": face.get("similarity", 0.0)
                    })
                elif face.get("status") == "detected":
                    # all模式下的简单检测结果
                    face_info.update({
                        "similarity": 0.0
                    })
                processed_faces.append(face_info)

        return processed_faces
    
    def _format_face_info(self, faces_info: List[Dict]) -> Dict[str, Any]:
        """
        格式化人脸信息
        
        Args:
            faces_info: 人脸信息列表
            
        Returns:
            格式化后的人脸信息
        """
        known_faces = []
        stranger_faces = []
        
        for face in faces_info:
            if face.get("status") == "known":
                known_faces.append({
                    "personId": face.get("personId", ""),
                    "name": face.get("name", ""),
                    "similarity": face.get("similarity", 0.0),
                    "bbox": face.get("bbox", [])
                })
            elif face.get("status") == "stranger":
                stranger_faces.append({
                    "similarity": face.get("similarity", 0.0),
                    "bbox": face.get("bbox", [])
                })
        
        return {
            "knownFaces": known_faces,
            "strangerFaces": stranger_faces,
            "totalCount": len(faces_info),
            "knownCount": len(known_faces),
            "strangerCount": len(stranger_faces)
        }
    
    def _draw_faces_on_image(self, image: np.ndarray, faces: List[Dict]) -> Optional[np.ndarray]:
        """
        在图像上绘制人脸框和信息
        
        Args:
            image: 原始图像
            faces: 人脸列表
            
        Returns:
            绘制后的图像
        """
        try:
            if image is None or len(faces) == 0:
                return image
            
            drawn_image = image.copy()
            
            for face in faces:
                bbox = face.get("bbox", [])
                if len(bbox) != 4:
                    continue
                
                x1, y1, x2, y2 = map(int, bbox)
                status = face.get("status", "unknown")
                
                # 根据状态选择颜色
                if status == "known":
                    color = (0, 255, 0)  # 绿色
                    label = face.get("name", "Known")
                elif status == "stranger":
                    color = (0, 0, 255)  # 红色
                    label = "Stranger"
                else:
                    color = (255, 255, 0)  # 黄色
                    label = "Unknown"
                
                # 绘制边界框
                cv2.rectangle(drawn_image, (x1, y1), (x2, y2), color, 2)
                
                # 绘制标签
                similarity = face.get("similarity", 0.0)
                text = f"{label} ({similarity:.2f})"
                
                # 计算文本尺寸
                font = cv2.FONT_HERSHEY_SIMPLEX
                font_scale = 0.6
                thickness = 2
                (text_width, text_height), _ = cv2.getTextSize(text, font, font_scale, thickness)
                
                # 绘制文本背景
                cv2.rectangle(drawn_image, (x1, y1 - text_height - 10), (x1 + text_width, y1), color, -1)
                
                # 绘制文本
                cv2.putText(drawn_image, text, (x1, y1 - 5), font, font_scale, (255, 255, 255), thickness)
                
                # 绘制关键点（如果有）
                landmarks = face.get("landmarks", [])
                if len(landmarks) == 10:  # 5个关键点，每个点2个坐标
                    for i in range(0, len(landmarks), 2):
                        if i + 1 < len(landmarks):
                            x, y = int(landmarks[i]), int(landmarks[i + 1])
                            cv2.circle(drawn_image, (x, y), 2, (255, 0, 0), -1)
            
            return drawn_image
            
        except Exception as e:
            mylog.error(f"绘制人脸框失败: {e}")
            return image
    
    def _image_to_base64(self, image: np.ndarray) -> Optional[str]:
        """
        将图像转换为base64字符串
        
        Args:
            image: 输入图像
            
        Returns:
            base64字符串
        """
        try:
            if image is None:
                return None
            
            # 编码为JPEG
            _, buffer = cv2.imencode('.jpg', image, [cv2.IMWRITE_JPEG_QUALITY, 90])
            
            # 转换为base64
            image_base64 = base64.b64encode(buffer).decode('utf-8')
            
            return f"data:image/jpeg;base64,{image_base64}"
            
        except Exception as e:
            mylog.error(f"图像转base64失败: {e}")
            return None
    
    def _has_valid_detection(self, faces_info: List[Dict], recognition_mode: str) -> bool:
        """
        判断是否有有效的检测结果 - 修复报警逻辑

        Args:
            faces_info: 人脸信息列表
            recognition_mode: 识别模式

        Returns:
            是否有有效检测（是否应该报警）
        """
        if not faces_info:
            return False

        # 根据不同模式判断是否应该报警
        if recognition_mode == "known_face":
            # 已知人脸模式：只有检测到已知人脸才报警
            known_faces = [face for face in faces_info if face.get("status") == "known"]
            should_alarm = len(known_faces) > 0
            if should_alarm:
                mylog.info(f"已知人脸模式：检测到{len(known_faces)}个已知人脸，触发报警")
            return should_alarm

        elif recognition_mode == "stranger_detection":
            # 陌生人检测模式：只有检测到陌生人才报警
            stranger_faces = [face for face in faces_info if face.get("status") == "stranger"]
            should_alarm = len(stranger_faces) > 0
            if should_alarm:
                mylog.info(f"陌生人检测模式：检测到{len(stranger_faces)}个陌生人，触发报警")
            return should_alarm

        else:  # all mode
            # 全部模式：检测到任何人脸都报警（不需要对比人脸库）
            should_alarm = len(faces_info) > 0
            if should_alarm:
                mylog.info(f"全部模式：检测到{len(faces_info)}张人脸，触发报警")
            return should_alarm
    
    def format_error_response(self, error_message: str, error_code: int = 500) -> Dict[str, Any]:
        """
        格式化错误响应
        
        Args:
            error_message: 错误消息
            error_code: 错误代码
            
        Returns:
            错误响应字典
        """
        return {
            "modelName": self.model_name,
            "modelVersion": self.model_version,
            "algoName": self.algo_name,
            "timestamp": int(time.time() * 1000),
            "status": "error",
            "errorCode": error_code,
            "message": error_message,
            "faceCount": 0,
            "inferResults": []
        }
