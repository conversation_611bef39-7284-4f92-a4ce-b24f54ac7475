{"modelName": "face_recognition", "modelNameEn": "model for face recognition and identification", "modelNameZh": "人脸识别与身份验证模型", "modelVersion": "v2.0.0", "algoSet": [{"algoName": "face_recognition", "algoNameEn": "face recognition and identification", "algoNameZh": "人脸识别与身份验证算法", "dataType": "image", "param": {"transactionNumber": "", "callBackData": "", "businessData": {"image": {"paramType": "String", "value": ""}, "imageType": {"paramType": "String", "value": "base64"}, "imageId": {"paramType": "number", "value": ""}, "advsetValue": {"area": {"areaType": {"display": false, "paramType": "String", "readType": "area", "required": true, "readonly": false, "options": [{"label": {"zh": "多边形", "en": "Polygon"}, "value": "POLYGON"}], "value": "POLYGON"}, "positions": {"display": false, "paramType": "List<List<float32>>", "readType": "area", "required": true, "readonly": false, "value": []}}, "interval": {"display": true, "paramType": "number", "readType": "inputNumber", "required": true, "readonly": false, "label": {"zh": "识别间隔(秒)", "en": "Recognition Interval(s)"}, "value": 5}, "recognitionMode": {"display": true, "paramType": "String", "readType": "select", "required": true, "readonly": false, "label": {"zh": "识别模式", "en": "Recognition Mode"}, "value": "known_face", "options": [{"label": {"zh": "已知人脸识别", "en": "Known Face Recognition"}, "value": "known_face"}, {"label": {"zh": "陌生人检测", "en": "Stranger Detection"}, "value": "stranger_detection"}, {"label": {"zh": "全部模式", "en": "All Mode"}, "value": "all"}]}, "similarityThreshold": {"display": true, "paramType": "number", "readType": "inputNumber", "required": true, "readonly": false, "label": {"zh": "相似度阈值", "en": "Similarity <PERSON><PERSON><PERSON><PERSON>"}, "value": 0.15, "min": 0.0, "max": 1.0, "step": 0.01}, "maxFaceSize": {"display": true, "paramType": "number", "readType": "inputNumber", "required": false, "readonly": false, "label": {"zh": "最大人脸尺寸", "en": "<PERSON> Face Si<PERSON>"}, "value": 640}, "recvDataType": {"display": true, "paramType": "number", "readType": "check", "required": true, "readonly": false, "label": {"zh": "结果输出类型", "en": "Response Type"}, "value": ["infer_results", "draw_image", "source_image", "face_info"], "options": [{"label": {"zh": "推理结果", "en": "Inference Result"}, "value": "infer_results"}, {"label": {"zh": "绘制后图片", "en": "Drawn Image"}, "value": "draw_image"}, {"label": {"zh": "原始图片", "en": "Original Image"}, "value": "source_image"}, {"label": {"zh": "人脸信息", "en": "Face Information"}, "value": "face_info"}]}}}}}]}