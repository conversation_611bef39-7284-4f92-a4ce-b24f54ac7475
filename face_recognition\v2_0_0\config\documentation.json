{"modelName": "face_recognition", "modelNameEn": "Face Recognition Algorithm", "modelNameZh": "人脸识别算法", "modelVersion": "v2.0.0", "algoSet": [{"algoName": "face_recognition", "algoNameEn": "Face Recognition and Identification", "algoNameZh": "人脸识别与身份验证", "dataType": "image", "param": {"transactionNumber": "", "callBackData": "", "businessData": {"image": {"paramType": "String", "value": ""}, "imageType": {"paramType": "String", "value": "base64"}, "imageId": {"paramType": "number", "value": ""}, "advsetValue": {"area": {"areaType": {"display": true, "paramType": "String", "readType": "area", "required": true, "readonly": false, "label": {"zh": "画框", "en": "Draw Box"}, "options": [{"label": {"zh": "多边形", "en": "Polygon"}, "value": "POLYGON"}], "value": "POLYGON"}, "positions": {"display": false, "paramType": "List<List<float32>>", "readType": "area", "required": true, "readonly": false, "value": []}}, "interval": {"display": true, "paramType": "number", "readType": "inputNumber", "required": true, "readonly": false, "label": {"zh": "报警时间间隔", "en": "Alarm Interval"}, "value": 30, "min": 1, "max": 3600, "step": 1, "unit": {"zh": "秒", "en": "seconds"}}, "recognitionMode": {"display": false, "paramType": "String", "readType": "select", "required": true, "readonly": false, "label": {"zh": "检测方式", "en": "Detection Mode"}, "value": "all", "options": [{"label": {"zh": "全部", "en": "All"}, "value": "all"}, {"label": {"zh": "已知人脸", "en": "Known Face"}, "value": "known_face"}, {"label": {"zh": "陌生人", "en": "Stranger"}, "value": "stranger_detection"}]}, "recvDataType": {"display": false, "paramType": "array", "readType": "checkbox", "required": true, "readonly": false, "label": {"zh": "结果输出类型", "en": "Output Type"}, "value": ["infer_results", "draw_image"], "options": [{"label": {"zh": "推理结果", "en": "Inference Results"}, "value": "infer_results"}, {"label": {"zh": "绘制图片", "en": "Draw Image"}, "value": "draw_image"}, {"label": {"zh": "原始图片", "en": "Source Image"}, "value": "source_image"}, {"label": {"zh": "人脸信息", "en": "Face Info"}, "value": "face_info"}]}}}}}]}