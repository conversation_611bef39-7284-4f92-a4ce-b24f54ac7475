{"modelName": "face_recognition", "modelNameEn": "model for face recognition and identification", "modelNameZh": "人脸识别与身份验证模型", "modelVersion": "v2.0.0", "algoSet": [{"algoName": "face_recognition", "algoNameEn": "face recognition and identification", "algoNameZh": "人脸识别与身份验证算法", "dataType": "image", "param": {"transactionNumber": "", "callBackData": "", "businessData": {"image": {"paramType": "String", "value": ""}, "imageType": {"paramType": "String", "value": "base64"}, "imageId": {"paramType": "number", "value": ""}, "advsetValue": {"area": {"areaType": {"display": true, "paramType": "String", "readType": "area", "required": true, "readonly": false, "label": {"zh": "画框区域", "en": "Detection Area"}, "options": [{"label": {"zh": "多边形", "en": "Polygon"}, "value": "POLYGON"}], "value": "POLYGON"}, "positions": {"display": false, "paramType": "List<List<float32>>", "readType": "area", "required": true, "readonly": false, "value": []}}, "interval": {"display": true, "paramType": "number", "readType": "inputNumber", "required": true, "readonly": false, "label": {"zh": "报警时间间隔(秒)", "en": "Alarm Interval(s)"}, "value": 30}, "recognitionMode": {"display": true, "paramType": "String", "readType": "select", "required": true, "readonly": false, "label": {"zh": "检测方式", "en": "Detection Mode"}, "value": "all", "options": [{"label": {"zh": "全部模式", "en": "All Mode"}, "value": "all"}, {"label": {"zh": "已知人脸识别", "en": "Known Face Recognition"}, "value": "known_face"}, {"label": {"zh": "陌生人检测", "en": "Stranger Detection"}, "value": "stranger_detection"}]}, "recvDataType": {"display": true, "paramType": "number", "readType": "check", "required": true, "readonly": false, "label": {"zh": "结果输出类型", "en": "Response Type"}, "value": ["infer_results", "draw_image"], "options": [{"label": {"zh": "推理结果", "en": "Inference Result"}, "value": "infer_results"}, {"label": {"zh": "绘制后图片", "en": "Drawn Image"}, "value": "draw_image"}, {"label": {"zh": "原始图片", "en": "Original Image"}, "value": "source_image"}, {"label": {"zh": "人脸信息", "en": "Face Information"}, "value": "face_info"}]}}}}}]}