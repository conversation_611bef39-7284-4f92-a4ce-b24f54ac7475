import cv2
import numpy as np
import base64
from PIL import Image
from typing import Tuple, Optional, Union
from ..utils.log import mylog


class PreProcess:
    """基础预处理类"""
    
    def __init__(self):
        """初始化预处理器"""
        self.default_size = (640, 640)
    
    def letterbox(self, img, new_shape=(640, 640), color=(114, 114, 114), auto=True, scaleFill=False, scaleup=True):
        """
        Letterbox图像预处理
        
        Args:
            img: 输入图像
            new_shape: 目标尺寸
            color: 填充颜色
            auto: 自动调整
            scaleFill: 缩放填充
            scaleup: 是否放大
            
        Returns:
            处理后的图像和缩放信息
        """
        try:
            shape = img.shape[:2]  # 当前形状 [height, width]
            if isinstance(new_shape, int):
                new_shape = (new_shape, new_shape)

            # 缩放比例 (new / old)
            r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
            if not scaleup:  # 只缩小，不放大
                r = min(r, 1.0)

            # 计算填充
            ratio = r, r  # 宽度、高度比例
            new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
            dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
            
            if auto:  # 最小矩形
                dw, dh = np.mod(dw, 32), np.mod(dh, 32)  # wh padding
            elif scaleFill:  # 拉伸
                dw, dh = 0.0, 0.0
                new_unpad = (new_shape[1], new_shape[0])
                ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]  # 宽度、高度比例

            dw /= 2  # 分割填充到两边
            dh /= 2

            if shape[::-1] != new_unpad:  # resize
                img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
            
            top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
            left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
            img = cv2.copyMakeBorder(img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)  # 添加边框
            
            return img, ratio, (dw, dh)
            
        except Exception as e:
            mylog.error(f"Letterbox处理失败: {e}")
            return img, (1.0, 1.0), (0, 0)


class FacePreProcess(PreProcess):
    """人脸识别预处理类"""
    
    def __init__(self):
        super().__init__()
        mylog.info('加载人脸识别图片前处理模块')
    
    def base64_to_image(self, base64_str: str, new_wh: Tuple[int, int] = (640, 640)) -> Tuple[Optional[np.ndarray], Optional[np.ndarray], Optional[Tuple]]:
        """
        将base64字符串转换为图像
        
        Args:
            base64_str: base64编码的图像字符串
            new_wh: 目标尺寸 (width, height)
            
        Returns:
            处理后的图像、原始图像、缩放信息
        """
        try:
            # 处理base64头部信息
            if 'data:image' in base64_str:
                base64_str = base64_str.split(',')[1]
            
            # 解码base64
            image_bytes = base64.b64decode(base64_str)
            nparr = np.frombuffer(image_bytes, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is None:
                mylog.error("base64图像解码失败")
                return None, None, None
            
            # Letterbox处理
            processed_img, gain, pad = self.letterbox(image, new_wh)
            
            return processed_img, image, (gain, pad)
            
        except Exception as e:
            mylog.error(f"base64图像处理失败: {e}")
            return None, None, None
    
    def file_to_image(self, file_path: str, new_wh: Tuple[int, int] = (640, 640)) -> Tuple[Optional[np.ndarray], Optional[np.ndarray], Optional[Tuple]]:
        """
        从文件路径读取图像
        
        Args:
            file_path: 图像文件路径
            new_wh: 目标尺寸 (width, height)
            
        Returns:
            处理后的图像、原始图像、缩放信息
        """
        try:
            image = cv2.imread(file_path)
            if image is None:
                mylog.error(f"无法读取图像文件: {file_path}")
                return None, None, None
            
            # Letterbox处理
            processed_img, gain, pad = self.letterbox(image, new_wh)
            
            return processed_img, image, (gain, pad)
            
        except Exception as e:
            mylog.error(f"文件图像处理失败: {e}")
            return None, None, None
    
    def numpy_to_image(self, image: np.ndarray, new_wh: Tuple[int, int] = (640, 640)) -> Tuple[Optional[np.ndarray], Optional[np.ndarray], Optional[Tuple]]:
        """
        处理numpy数组图像
        
        Args:
            image: numpy数组图像
            new_wh: 目标尺寸 (width, height)
            
        Returns:
            处理后的图像、原始图像、缩放信息
        """
        try:
            if image is None or len(image.shape) != 3:
                mylog.error("无效的numpy图像数组")
                return None, None, None
            
            # Letterbox处理
            processed_img, gain, pad = self.letterbox(image, new_wh)
            
            return processed_img, image, (gain, pad)
            
        except Exception as e:
            mylog.error(f"numpy图像处理失败: {e}")
            return None, None, None
    
    def get_data(self, data: dict) -> Tuple[Optional[np.ndarray], Optional[np.ndarray], Optional[Tuple]]:
        """
        根据数据字典获取处理后的图像
        
        Args:
            data: 包含图像数据的字典
            
        Returns:
            处理后的图像、原始图像、缩放信息
        """
        try:
            business_data = data.get("businessData", {})
            image_type = business_data.get("imageType", "base64")
            image_data = business_data.get("image")
            
            if not image_data:
                mylog.error("图像数据为空")
                return None, None, None
            
            # 获取目标尺寸
            advset_value = business_data.get("advsetValue", {})
            max_face_size = advset_value.get("maxFaceSize", 640)
            new_wh = (max_face_size, max_face_size)
            
            if image_type == "base64":
                return self.base64_to_image(image_data, new_wh)
            elif image_type == "file":
                return self.file_to_image(image_data, new_wh)
            elif image_type == "numpy":
                return self.numpy_to_image(image_data, new_wh)
            else:
                mylog.error(f"不支持的图像类型: {image_type}")
                return None, None, None
                
        except Exception as e:
            mylog.error(f"获取图像数据失败: {e}")
            return None, None, None
    
    def preprocess_for_detection(self, image: np.ndarray) -> Optional[np.ndarray]:
        """
        人脸检测专用预处理
        
        Args:
            image: 输入图像
            
        Returns:
            预处理后的图像
        """
        try:
            # 转换颜色空间
            if len(image.shape) == 3 and image.shape[2] == 3:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 归一化
            image = image.astype(np.float32)
            image -= np.array((104, 117, 123), np.float32)
            
            # 添加batch维度
            image = np.expand_dims(image, axis=0)
            
            return image
            
        except Exception as e:
            mylog.error(f"检测预处理失败: {e}")
            return None
    
    def preprocess_for_recognition(self, face_image: np.ndarray, target_size: Tuple[int, int] = (112, 112)) -> Optional[np.ndarray]:
        """
        人脸识别专用预处理
        
        Args:
            face_image: 人脸图像
            target_size: 目标尺寸
            
        Returns:
            预处理后的图像
        """
        try:
            # 确保是RGB格式
            if len(face_image.shape) == 3 and face_image.shape[2] == 3:
                face_image = cv2.cvtColor(face_image, cv2.COLOR_BGR2RGB)
            
            # Resize
            if face_image.shape[:2] != target_size[::-1]:
                face_image = cv2.resize(face_image, target_size)
            
            # 归一化到[-1, 1]
            face_image = face_image.astype(np.float32) / 255.0
            face_image = (face_image - 0.5) / 0.5
            
            # 转换维度顺序 (H, W, C) -> (C, H, W)
            face_image = np.transpose(face_image, (2, 0, 1))
            
            # 添加batch维度
            face_image = np.expand_dims(face_image, axis=0)
            
            return face_image
            
        except Exception as e:
            mylog.error(f"识别预处理失败: {e}")
            return None
    
    def validate_image(self, image: np.ndarray) -> bool:
        """
        验证图像是否有效
        
        Args:
            image: 输入图像
            
        Returns:
            是否有效
        """
        try:
            if image is None:
                return False
            
            if len(image.shape) != 3:
                return False
            
            if image.shape[2] != 3:
                return False
            
            if image.shape[0] < 32 or image.shape[1] < 32:
                return False
            
            return True
            
        except Exception:
            return False
