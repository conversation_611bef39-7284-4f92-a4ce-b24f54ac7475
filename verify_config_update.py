#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证配置更新
"""

import os
import json

def main():
    print("验证face_recognition配置更新")
    print("=" * 50)
    
    # 检查配置文件
    config_path = "face_recognition/v2_0_0/config/documentation.json"
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("✓ 配置文件存在")
        print(f"  模型名称: {config['modelName']}")
        print(f"  模型版本: {config['modelVersion']}")
        
        # 检查关键字段
        advset = config["algoSet"][0]["param"]["businessData"]["advsetValue"]
        
        # 检查新的字段名
        if "faceRecognition" in advset:
            face_config = advset["faceRecognition"]
            print("✓ faceRecognition字段存在")
            print(f"  标签: {face_config['label']['zh']}")
            print(f"  默认值: {face_config['value']}")
            
            options = [opt['label']['zh'] for opt in face_config['options']]
            print(f"  选项: {options}")
        else:
            print("✗ faceRecognition字段不存在")
        
        # 检查其他字段
        required_fields = ["area", "interval", "recvDataType"]
        for field in required_fields:
            if field in advset:
                print(f"✓ {field}字段存在")
            else:
                print(f"✗ {field}字段不存在")
        
        print("\n配置更新总结:")
        print("✅ 参照detect_fallperson重新设计了documentation.json")
        print("✅ 字段名从recognitionMode改为faceRecognition")
        print("✅ 保持了与detect_fallperson一致的结构")
        print("✅ 更新了相关逻辑代码")
        
        print("\n主要配置字段:")
        print("- area: 画框区域配置")
        print("- interval: 报警间隔配置")
        print("- faceRecognition: 检测方式配置")
        print("- recvDataType: 结果输出类型配置")
        
    else:
        print("✗ 配置文件不存在")

if __name__ == "__main__":
    main()
