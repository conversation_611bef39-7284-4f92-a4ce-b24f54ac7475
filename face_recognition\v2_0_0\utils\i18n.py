import json
import os
from typing import Dict, Any


class I18n:
    """国际化支持类"""
    
    def __init__(self, default_language: str = "zh"):
        """
        初始化国际化支持
        
        Args:
            default_language: 默认语言，支持 'zh' 和 'en'
        """
        self.current_language = default_language
        self.translations = {}
        self.base_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'i18n')
        self._load_translations()
    
    def _load_translations(self):
        """加载翻译文件"""
        try:
            # 加载中文翻译
            zh_file = os.path.join(self.base_path, 'zh.json')
            if os.path.exists(zh_file):
                with open(zh_file, 'r', encoding='utf-8') as f:
                    self.translations['zh'] = json.load(f)
            
            # 加载英文翻译
            en_file = os.path.join(self.base_path, 'en.json')
            if os.path.exists(en_file):
                with open(en_file, 'r', encoding='utf-8') as f:
                    self.translations['en'] = json.load(f)
                    
        except Exception as e:
            print(f"Failed to load translations: {e}")
            # 设置默认翻译
            self.translations = {
                'zh': {'messages': {'processing_error': '处理过程中发生错误'}},
                'en': {'messages': {'processing_error': 'Error occurred during processing'}}
            }
    
    def get(self, key: str, default: str = None) -> str:
        """
        获取翻译文本
        
        Args:
            key: 翻译键，支持嵌套键如 'messages.face_detected'
            default: 默认值
            
        Returns:
            翻译后的文本
        """
        try:
            keys = key.split('.')
            value = self.translations.get(self.current_language, {})
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    # 如果当前语言没有找到，尝试使用英文
                    if self.current_language != 'en':
                        value = self.translations.get('en', {})
                        for k in keys:
                            if isinstance(value, dict) and k in value:
                                value = value[k]
                            else:
                                value = default or key
                                break
                    else:
                        value = default or key
                    break
            
            return str(value) if value is not None else (default or key)
            
        except Exception:
            return default or key
    
    def set_language(self, language: str) -> bool:
        """
        设置当前语言
        
        Args:
            language: 语言代码 ('zh' 或 'en')
            
        Returns:
            是否设置成功
        """
        if language in self.translations:
            self.current_language = language
            return True
        return False
    
    def get_current_language(self) -> str:
        """获取当前语言"""
        return self.current_language
    
    def reload(self, language: str = None) -> Dict[str, Any]:
        """
        重新加载翻译文件
        
        Args:
            language: 可选的新语言设置
            
        Returns:
            操作结果
        """
        try:
            self._load_translations()
            if language and language in self.translations:
                self.current_language = language
            
            return {
                "code": 200,
                "message": self.get("messages.model_load_success"),
                "current_language": self.current_language,
                "available_languages": list(self.translations.keys())
            }
        except Exception as e:
            return {
                "code": 500,
                "message": f"Failed to reload translations: {e}",
                "current_language": self.current_language
            }
    
    def get_all_translations(self) -> Dict[str, Any]:
        """获取所有翻译数据"""
        return self.translations
    
    def format_message(self, key: str, **kwargs) -> str:
        """
        格式化消息（支持参数替换）
        
        Args:
            key: 翻译键
            **kwargs: 格式化参数
            
        Returns:
            格式化后的消息
        """
        message = self.get(key)
        try:
            return message.format(**kwargs)
        except Exception:
            return message
