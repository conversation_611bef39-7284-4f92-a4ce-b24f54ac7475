#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速API测试
"""

import requests
import json
import time

def quick_test():
    """快速测试API调用"""
    print("🚀 快速API测试")
    
    # 简单的测试图片base64（1x1像素PNG）
    test_image = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    
    # 请求数据
    data = {
        "transactionNumber": f"test-{int(time.time())}",
        "callBackData": "test_data",
        "businessData": {
            "image": test_image,
            "imageType": "base64",
            "imageId": "test_001",
            "callbackUrl": "http://192.168.3.202:8080/callback",  # 回调URL
            "advsetValue": {
                "area": {
                    "areaType": "POLYGON",
                    "positions": [[0, 0], [640, 0], [640, 480], [0, 480]]
                },
                "interval": 1,
                "faceRecognition": "all",  # all模式
                "recvDataType": ["infer_results", "draw_image"]
            }
        }
    }
    
    print(f"📋 发送请求到: http://192.168.3.202:15000/ai/infer")
    print(f"🔗 回调URL: {data['businessData']['callbackUrl']}")
    print(f"🎯 检测模式: {data['businessData']['advsetValue']['faceRecognition']}")
    
    try:
        response = requests.post(
            "http://192.168.3.202:15000/ai/infer",
            json=data,
            timeout=30
        )
        
        print(f"📊 状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功!")
            print(f"响应: {json.dumps(result, ensure_ascii=False, indent=2)}")
        else:
            print(f"❌ 失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    quick_test()
