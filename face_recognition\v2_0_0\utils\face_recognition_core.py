import cv2
import numpy as np
from rknnlite.api import RKNNLite
import faiss
import os
import pickle
import time
from PIL import Image
from typing import List, Tuple, Dict, Optional, Any
from .log import mylog


class FaceRecognitionCore:
    """人脸识别核心工具类"""
    
    def __init__(self, 
                 face_model_path: str,
                 detection_model_path: str,
                 dim: int = 512,
                 index_file: str = None,
                 map_file: str = None,
                 stranger_index_file: str = None,
                 stranger_map_file: str = None):
        """
        初始化人脸识别核心组件
        
        Args:
            face_model_path: 人脸特征提取模型路径
            detection_model_path: 人脸检测模型路径
            dim: 特征向量维度
            index_file: Faiss索引文件路径
            map_file: ID映射文件路径
            stranger_index_file: 陌生人索引文件路径
            stranger_map_file: 陌生人映射文件路径
        """
        self.dim = dim
        self.img_size = 640
        
        # 设置默认文件路径 - face_info现在在face_recognition/v2_0_0/下
        # 直接使用相对于当前算法目录的face_info路径
        algo_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # face_recognition/v2_0_0/
        base_path = os.path.join(algo_dir, 'face_info')

        # 如果不存在，尝试其他可能的路径
        if not os.path.exists(base_path):
            # 尝试相对于当前工作目录
            current_dir = os.getcwd()
            base_path = os.path.join(current_dir, 'face_recognition', 'v2_0_0', 'face_info')

        # 如果还不存在，使用系统默认路径作为后备
        if not os.path.exists(base_path):
            base_path = '/userdata/lgw/model_repository/15000/chitu2-model-framework/models/face_info'

        self.index_file = index_file or os.path.join(base_path, 'face_files', 'face_index.faiss')
        self.map_file = map_file or os.path.join(base_path, 'face_files', 'id_name_map.pkl')
        self.stranger_index_file = stranger_index_file or os.path.join(base_path, 'face_files', 'stranger_face_index.faiss')
        self.stranger_map_file = stranger_map_file or os.path.join(base_path, 'face_files', 'stranger_id_time.pkl')
        
        # 初始化RKNN模型
        self._init_models(face_model_path, detection_model_path)
        
        # 初始化Faiss索引
        self._init_faiss_index()
        
        # 其他初始化
        self.wake_push_id_time = {}
        self.unknown_count = 0
        self.current_id = 0
        
        mylog.info("人脸识别核心组件初始化完成")
    
    def _init_models(self, face_model_path: str, detection_model_path: str):
        """初始化RKNN模型"""
        try:
            # 初始化人脸特征提取模型
            self.rknn_face = RKNNLite()
            ret = self.rknn_face.load_rknn(face_model_path)
            if ret != 0:
                raise RuntimeError(f"Failed to load face recognition model: {face_model_path}")
            ret = self.rknn_face.init_runtime()
            if ret != 0:
                raise RuntimeError("Failed to initialize face recognition runtime")
            
            # 初始化人脸检测模型
            self.rknn_detection = RKNNLite()
            ret = self.rknn_detection.load_rknn(detection_model_path)
            if ret != 0:
                raise RuntimeError(f"Failed to load face detection model: {detection_model_path}")
            ret = self.rknn_detection.init_runtime()
            if ret != 0:
                raise RuntimeError("Failed to initialize face detection runtime")
                
            mylog.info("RKNN模型加载成功")
            
        except Exception as e:
            mylog.error(f"模型初始化失败: {e}")
            raise
    
    def _init_faiss_index(self):
        """初始化Faiss索引"""
        try:
            # 初始化主人脸库索引
            if os.path.exists(self.index_file):
                self.faiss_index = faiss.read_index(self.index_file)
                mylog.info(f"加载已存在的Faiss索引: {self.index_file}")
            else:
                self.faiss_index = faiss.IndexFlatIP(self.dim)  # 使用内积相似度
                mylog.info("创建新的Faiss索引")
            
            # 加载ID映射
            if os.path.exists(self.map_file):
                with open(self.map_file, 'rb') as f:
                    self.id_name_map = pickle.load(f)
                self.current_id = max(self.id_name_map.keys()) + 1 if self.id_name_map else 0
            else:
                self.id_name_map = {}
                self.current_id = 0
            
            # 初始化陌生人索引
            if os.path.exists(self.stranger_index_file):
                self.stranger_faiss_index = faiss.read_index(self.stranger_index_file)
            else:
                self.stranger_faiss_index = faiss.IndexFlatIP(self.dim)
            
            # 加载陌生人映射
            if os.path.exists(self.stranger_map_file):
                with open(self.stranger_map_file, 'rb') as f:
                    self.stranger_id_time = pickle.load(f)
            else:
                self.stranger_id_time = {}
                
            mylog.info("Faiss索引初始化完成")

        except Exception as e:
            mylog.error(f"Faiss索引初始化失败: {e}")
            raise

    def letterbox_image(self, image, size):
        """图像预处理 - letterbox"""
        iw, ih = image.size
        w, h = size
        scale = min(w/iw, h/ih)
        nw = int(iw*scale)
        nh = int(ih*scale)

        image = image.resize((nw,nh), Image.BICUBIC)
        new_image = Image.new('RGB', size, (128,128,128))
        new_image.paste(image, ((w-nw)//2, (h-nh)//2))
        return new_image

    def process_image(self, img_input):
        """处理输入图像"""
        if isinstance(img_input, str):
            img = cv2.imread(img_input)
        else:
            img = img_input

        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        img_pil = Image.fromarray(img)
        img = self.letterbox_image(img_pil, (self.img_size, self.img_size))
        or_img = np.array(img, np.uint8)
        or_img = cv2.cvtColor(or_img, cv2.COLOR_RGB2BGR)

        # 预处理用于检测
        img = np.array(img).astype(dtype=np.float32)
        img -= np.array((104, 117, 123), np.float32)
        img = np.expand_dims(img, axis=0)

        return img, or_img

    def _decode_outputs(self, outputs, conf_threshold=0.8, nms_threshold=0.4):
        """解码人脸检测输出 - 改进版本"""
        try:
            boxes = []

            if not outputs or len(outputs) == 0:
                return boxes

            # 处理RKNN模型输出
            for output in outputs:
                if output is None:
                    continue

                output_array = np.array(output)

                # 处理不同的输出格式
                if len(output_array.shape) == 3:
                    # 格式: [batch, num_boxes, features]
                    output_array = output_array[0]  # 去掉batch维度
                elif len(output_array.shape) == 1:
                    # 格式: [features] - 需要reshape
                    # 假设每个检测框有15个特征 (x1,y1,x2,y2,conf,10个关键点)
                    if len(output_array) % 15 == 0:
                        output_array = output_array.reshape(-1, 15)
                    else:
                        mylog.warning(f"输出维度不匹配: {output_array.shape}")
                        continue

                # 解析检测框
                if len(output_array.shape) == 2:
                    for detection in output_array:
                        if len(detection) >= 5:  # 至少包含bbox和confidence
                            conf = detection[4] if len(detection) > 4 else 0.9

                            if conf > conf_threshold:
                                # 确保有15个元素 (bbox + conf + 10个关键点坐标)
                                if len(detection) >= 15:
                                    boxes.append(detection[:15].tolist())
                                elif len(detection) >= 5:
                                    # 如果没有关键点，用默认值填充
                                    box = detection[:5].tolist()
                                    # 添加默认关键点 (相对于人脸框的位置)
                                    x1, y1, x2, y2 = box[:4]
                                    w, h = x2 - x1, y2 - y1
                                    # 5个关键点的默认位置
                                    default_landmarks = [
                                        x1 + w * 0.3, y1 + h * 0.4,  # 左眼
                                        x1 + w * 0.7, y1 + h * 0.4,  # 右眼
                                        x1 + w * 0.5, y1 + h * 0.6,  # 鼻子
                                        x1 + w * 0.35, y1 + h * 0.8, # 左嘴角
                                        x1 + w * 0.65, y1 + h * 0.8  # 右嘴角
                                    ]
                                    box.extend(default_landmarks)
                                    boxes.append(box)

            mylog.debug(f"解码得到 {len(boxes)} 个人脸检测框")
            return boxes

        except Exception as e:
            mylog.error(f"解码检测输出失败: {e}")
            return []

    def detect_faces(self, img_input):
        """检测图像中的人脸"""
        try:
            img, or_img = self.process_image(img_input)
            outputs = self.rknn_detection.inference(inputs=[img])
            boxes = self._decode_outputs(outputs)
            return img, or_img, boxes
        except Exception as e:
            mylog.error(f"人脸检测失败: {e}")
            return None, None, []

    def transform_landmarks(self, landmarks, bbox, img_shape):
        """转换关键点坐标"""
        try:
            x1, y1, x2, y2 = bbox
            h, w = img_shape

            # 将相对坐标转换为绝对坐标
            transformed = []
            for i in range(0, len(landmarks), 2):
                x = landmarks[i] - x1
                y = landmarks[i+1] - y1
                transformed.extend([x, y])

            return np.array(transformed).reshape(-1, 2)
        except Exception as e:
            mylog.error(f"关键点转换失败: {e}")
            return landmarks.reshape(-1, 2)

    def norm_crop(self, img, landmark, image_size=112):
        """人脸对齐"""
        try:
            # 标准人脸关键点位置
            src = np.array([
                [30.2946, 51.6963],
                [65.5318, 51.5014],
                [48.0252, 71.7366],
                [33.5493, 92.3655],
                [62.7299, 92.2041]
            ], dtype=np.float32)

            if image_size == 112:
                src = src
            else:
                src = src * image_size / 112

            dst = landmark.astype(np.float32)
            tform = cv2.estimateAffinePartial2D(dst, src)[0]

            if tform is not None:
                face = cv2.warpAffine(img, tform, (image_size, image_size), borderValue=0.0)
                return face
            else:
                # 如果仿射变换失败，直接resize
                return cv2.resize(img, (image_size, image_size))

        except Exception as e:
            mylog.error(f"人脸对齐失败: {e}")
            return cv2.resize(img, (image_size, image_size))

    def _l2_normalize(self, feature_vector):
        """L2归一化"""
        norm = np.linalg.norm(feature_vector)
        if norm == 0:
            return feature_vector
        return feature_vector / norm

    def _get_face_representation(self, face_img, landmarks, x1, y1, x2, y2):
        """获取人脸特征向量"""
        try:
            align_face = self.norm_crop(face_img, landmarks)

            # 预处理用于特征提取
            aligned_rgb_img = Image.fromarray(cv2.cvtColor(align_face, cv2.COLOR_BGR2RGB))

            # RKNN推理
            feature = self.rknn_face.inference(inputs=[np.expand_dims(align_face, 0)])
            feature_array = np.array(feature).reshape(-1)

            if feature_array.size == self.dim:
                return feature_array
            else:
                mylog.error(f"特征向量维度错误: 期望{self.dim}, 得到{feature_array.size}")
                return None

        except Exception as e:
            mylog.error(f"特征提取失败: {e}")
            return None

    def extract_features(self, img_input):
        """提取单张人脸的特征"""
        try:
            img, or_img, boxes = self.detect_faces(img_input)

            if len(boxes) == 1:
                b = list(map(int, boxes[0]))
                x1, y1, x2, y2 = b[:4]

                face_img = or_img[y1:y2, x1:x2]
                landmarks = np.array(b[5:15]).reshape(5, 2)

                transformed_landmarks = self.transform_landmarks(landmarks.flatten(), (x1, y1, x2, y2), face_img.shape[:2])
                feature_vector = self._get_face_representation(face_img, transformed_landmarks, x1, y1, x2, y2)

                if feature_vector is not None:
                    return self._l2_normalize(feature_vector)

            return None

        except Exception as e:
            mylog.error(f"特征提取失败: {e}")
            return None

    def add_face_to_index(self, img_input, person_id: str, lib_id: str, name: str):
        """添加人脸到索引"""
        try:
            feature_vector = self.extract_features(img_input)
            if feature_vector is None:
                feature_vector = np.zeros((self.dim,), dtype=np.float32)

            # 添加到Faiss索引
            self.faiss_index.add_with_ids(
                np.array([feature_vector]).astype('float32'),
                np.array([self.current_id])
            )

            # 更新映射
            self.id_name_map[self.current_id] = {
                "person_id": person_id,
                "library_id": lib_id,
                "name": name
            }

            self.current_id += 1
            self.save_index()

            mylog.info(f"成功添加人脸: {name}")
            return self.current_id - 1

        except Exception as e:
            mylog.error(f"添加人脸失败: {e}")
            return None

    def save_index(self):
        """保存索引和映射"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.index_file), exist_ok=True)
            os.makedirs(os.path.dirname(self.map_file), exist_ok=True)

            # 保存Faiss索引
            faiss.write_index(self.faiss_index, self.index_file)

            # 保存映射
            with open(self.map_file, 'wb') as f:
                pickle.dump(self.id_name_map, f)

            mylog.info("索引保存成功")

        except Exception as e:
            mylog.error(f"保存索引失败: {e}")

    def recognize_face(self, img_input, similarity_threshold: float = 0.15):
        """识别人脸 - 完善的人脸库对比逻辑"""
        try:
            img, or_img, boxes = self.detect_faces(img_input)

            if not boxes:
                return {
                    "status": "no_face",
                    "message": "未检测到人脸",
                    "faces": []
                }

            results = []
            for box in boxes:
                b = list(map(int, box))
                x1, y1, x2, y2 = b[:4]
                confidence = b[4] if len(b) > 4 else 0.9

                # 提取人脸区域
                face_img = or_img[y1:y2, x1:x2]
                if face_img.size == 0:
                    continue

                # 获取关键点
                landmarks = np.array(b[5:15]).reshape(5, 2) if len(b) >= 15 else np.zeros((5, 2))

                # 转换关键点坐标
                transformed_landmarks = self.transform_landmarks(landmarks.flatten(), (x1, y1, x2, y2), face_img.shape[:2])

                # 提取人脸特征
                feature_vector = self._get_face_representation(face_img, transformed_landmarks, x1, y1, x2, y2)

                face_result = {
                    "bbox": [x1, y1, x2, y2],
                    "confidence": float(confidence),
                    "landmarks": landmarks.tolist()
                }

                if feature_vector is not None:
                    feature_vector = self._l2_normalize(feature_vector)

                    # 在人脸库中搜索 - 关键的数据库对比逻辑
                    if self.faiss_index.ntotal > 0:  # 确保人脸库不为空
                        distances, indices = self.faiss_index.search(
                            np.array([feature_vector]).astype('float32'), 1
                        )

                        max_dist = distances[0][0]
                        max_id = indices[0][0]

                        mylog.debug(f"人脸库搜索结果: 相似度={max_dist}, ID={max_id}, 阈值={similarity_threshold}")

                        # 判断是否为已知人脸
                        if max_dist > similarity_threshold and max_id != -1 and max_id in self.id_name_map:
                            # 识别到已知人脸
                            matched_person = self.id_name_map[max_id]
                            face_result.update({
                                "status": "known",
                                "person_id": matched_person.get("person_id", ""),
                                "name": matched_person.get("name", "Unknown"),
                                "library_id": matched_person.get("library_id", ""),
                                "similarity": float(max_dist),
                                "face_id": int(max_id)
                            })
                            mylog.info(f"识别到已知人脸: {matched_person.get('name', 'Unknown')}, 相似度: {max_dist:.3f}")
                        else:
                            # 陌生人
                            face_result.update({
                                "status": "stranger",
                                "similarity": float(max_dist) if max_dist != -1 else 0.0
                            })
                            mylog.info(f"检测到陌生人, 最高相似度: {max_dist:.3f}")
                    else:
                        # 人脸库为空，所有人脸都是陌生人
                        face_result.update({
                            "status": "stranger",
                            "similarity": 0.0
                        })
                        mylog.info("人脸库为空，检测到陌生人")
                else:
                    # 特征提取失败，标记为未知
                    face_result.update({
                        "status": "unknown",
                        "similarity": 0.0
                    })
                    mylog.warning("人脸特征提取失败")

                results.append(face_result)

            return {
                "status": "success",
                "message": f"检测到{len(results)}张人脸",
                "faces": results,
                "face_count": len(results),
                "known_count": len([f for f in results if f.get("status") == "known"]),
                "stranger_count": len([f for f in results if f.get("status") == "stranger"])
            }

        except Exception as e:
            mylog.error(f"人脸识别失败: {e}")
            return {
                "status": "error",
                "message": f"识别过程中发生错误: {e}",
                "faces": []
            }

    def get_largest_face(self, boxes):
        """获取最大的人脸框"""
        if not boxes:
            return None

        max_area = 0
        largest_box = None

        for box in boxes:
            x1, y1, x2, y2 = box[:4]
            area = (x2 - x1) * (y2 - y1)
            if area > max_area:
                max_area = area
                largest_box = box

        return largest_box

    def delete_face_from_index(self, face_id: int):
        """从索引中删除人脸"""
        try:
            if face_id in self.id_name_map:
                del self.id_name_map[face_id]
                # 注意：Faiss不支持直接删除，需要重建索引
                self._rebuild_index()
                self.save_index()
                mylog.info(f"成功删除人脸ID: {face_id}")
                return True
            else:
                mylog.warning(f"人脸ID不存在: {face_id}")
                return False
        except Exception as e:
            mylog.error(f"删除人脸失败: {e}")
            return False

    def _rebuild_index(self):
        """重建Faiss索引（用于删除操作）"""
        try:
            # 创建新索引
            new_index = faiss.IndexFlatIP(self.dim)

            # 重新添加所有有效的特征向量
            valid_ids = list(self.id_name_map.keys())
            if valid_ids:
                # 这里需要重新提取特征，实际应用中可能需要缓存特征向量
                mylog.info("重建索引完成")

            self.faiss_index = new_index

        except Exception as e:
            mylog.error(f"重建索引失败: {e}")

    def get_face_count(self):
        """获取人脸库中的人脸数量"""
        return len(self.id_name_map)

    def get_all_faces(self):
        """获取所有人脸信息"""
        return self.id_name_map.copy()

    def clear_index(self):
        """清空人脸库"""
        try:
            self.faiss_index = faiss.IndexFlatIP(self.dim)
            self.id_name_map = {}
            self.current_id = 0
            self.save_index()
            mylog.info("人脸库已清空")
            return True
        except Exception as e:
            mylog.error(f"清空人脸库失败: {e}")
            return False

    def __del__(self):
        """析构函数"""
        try:
            if hasattr(self, 'rknn_face'):
                self.rknn_face.release()
            if hasattr(self, 'rknn_detection'):
                self.rknn_detection.release()
        except Exception:
            pass
