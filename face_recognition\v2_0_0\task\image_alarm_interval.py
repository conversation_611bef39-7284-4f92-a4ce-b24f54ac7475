import time
from typing import Dict, Tuple
from ..utils.log import mylog


def contrast_time(interval_image: Dict[str, float], transaction_number: str, interval: int) -> Tuple[bool, Dict[str, float]]:
    """
    对比时间间隔，判断是否应该触发报警
    
    Args:
        interval_image: 间隔图像字典，存储每个事务的最后触发时间
        transaction_number: 事务编号
        interval: 间隔时间（秒）
        
    Returns:
        (是否应该报警, 更新后的间隔图像字典)
    """
    try:
        current_time = time.time()
        
        # 如果是第一次检测到该事务，直接触发报警
        if transaction_number not in interval_image:
            interval_image[transaction_number] = current_time
            mylog.info(f"首次检测到事务 {transaction_number}，触发报警")
            return True, interval_image
        
        # 计算时间差
        last_time = interval_image[transaction_number]
        time_diff = current_time - last_time
        
        # 如果超过间隔时间，触发报警并更新时间
        if time_diff >= interval:
            interval_image[transaction_number] = current_time
            mylog.info(f"事务 {transaction_number} 超过间隔时间 {interval}秒，触发报警")
            return True, interval_image
        else:
            mylog.debug(f"事务 {transaction_number} 未超过间隔时间，剩余 {interval - time_diff:.1f}秒")
            return False, interval_image
            
    except Exception as e:
        mylog.error(f"时间对比失败: {e}")
        return False, interval_image


def cleanup_old_intervals(interval_image: Dict[str, float], max_age: int = 3600) -> Dict[str, float]:
    """
    清理过期的间隔记录
    
    Args:
        interval_image: 间隔图像字典
        max_age: 最大保留时间（秒），默认1小时
        
    Returns:
        清理后的间隔图像字典
    """
    try:
        current_time = time.time()
        cleaned_dict = {}
        
        for transaction_number, last_time in interval_image.items():
            if current_time - last_time <= max_age:
                cleaned_dict[transaction_number] = last_time
        
        removed_count = len(interval_image) - len(cleaned_dict)
        if removed_count > 0:
            mylog.info(f"清理了 {removed_count} 个过期的间隔记录")
        
        return cleaned_dict
        
    except Exception as e:
        mylog.error(f"清理间隔记录失败: {e}")
        return interval_image


class IntervalManager:
    """间隔管理器"""
    
    def __init__(self, default_interval: int = 30, max_age: int = 3600):
        """
        初始化间隔管理器
        
        Args:
            default_interval: 默认间隔时间（秒）
            max_age: 记录最大保留时间（秒）
        """
        self.default_interval = default_interval
        self.max_age = max_age
        self.interval_records = {}
        self.last_cleanup = time.time()
        self.cleanup_interval = 300  # 5分钟清理一次
    
    def should_trigger(self, transaction_number: str, interval: int = None) -> bool:
        """
        判断是否应该触发报警
        
        Args:
            transaction_number: 事务编号
            interval: 间隔时间，如果为None则使用默认值
            
        Returns:
            是否应该触发
        """
        if interval is None:
            interval = self.default_interval
        
        # 定期清理过期记录
        self._periodic_cleanup()
        
        should_trigger, self.interval_records = contrast_time(
            self.interval_records, 
            transaction_number, 
            interval
        )
        
        return should_trigger
    
    def _periodic_cleanup(self):
        """定期清理过期记录"""
        current_time = time.time()
        if current_time - self.last_cleanup > self.cleanup_interval:
            self.interval_records = cleanup_old_intervals(self.interval_records, self.max_age)
            self.last_cleanup = current_time
    
    def get_records_count(self) -> int:
        """获取当前记录数量"""
        return len(self.interval_records)
    
    def clear_records(self):
        """清空所有记录"""
        self.interval_records.clear()
        mylog.info("已清空所有间隔记录")
    
    def get_record_info(self, transaction_number: str) -> Dict[str, any]:
        """
        获取指定事务的记录信息
        
        Args:
            transaction_number: 事务编号
            
        Returns:
            记录信息字典
        """
        if transaction_number in self.interval_records:
            last_time = self.interval_records[transaction_number]
            current_time = time.time()
            return {
                "transaction_number": transaction_number,
                "last_trigger_time": last_time,
                "time_since_last": current_time - last_time,
                "exists": True
            }
        else:
            return {
                "transaction_number": transaction_number,
                "exists": False
            }
