import os
import time
from rknnlite.api import RKNNLite
from ..utils.log import mylog


class ModelCheck:
    """模型检查和推理类"""
    
    def __init__(self, labels, weight_name, model_id=0):
        """
        初始化模型检查器
        
        Args:
            labels: 标签列表
            weight_name: 权重文件名（不含扩展名）
            model_id: 模型ID，用于多模型并行
        """
        self.labels = labels
        self.weight_name = weight_name
        self.model_id = model_id
        self.rknn = None
        
        # 获取权重文件路径
        self.weight_path = self._get_weight_path(weight_name)
        
        # 初始化模型
        self._init_model()
    
    def _get_weight_path(self, weight_name):
        """获取权重文件路径"""
        # 首先检查face_info目录
        base_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), 
            'face_info', 'weights'
        )
        weight_file = os.path.join(base_path, f"{weight_name}.rknn")
        
        if os.path.exists(weight_file):
            return weight_file
        
        # 检查当前算法的weights目录
        current_weights_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
            'weights'
        )
        weight_file = os.path.join(current_weights_path, f"{weight_name}.rknn")
        
        if os.path.exists(weight_file):
            return weight_file
        
        # 如果都找不到，返回默认路径
        mylog.warning(f"权重文件未找到: {weight_name}.rknn，使用默认路径")
        return os.path.join(current_weights_path, f"{weight_name}.rknn")
    
    def _init_model(self):
        """初始化RKNN模型"""
        try:
            if not os.path.exists(self.weight_path):
                raise FileNotFoundError(f"权重文件不存在: {self.weight_path}")
            
            self.rknn = RKNNLite()
            
            # 加载模型
            ret = self.rknn.load_rknn(self.weight_path)
            if ret != 0:
                raise RuntimeError(f"加载RKNN模型失败: {self.weight_path}")
            
            # 初始化运行时
            ret = self.rknn.init_runtime()
            if ret != 0:
                raise RuntimeError(f"初始化RKNN运行时失败: {self.weight_path}")
            
            mylog.info(f"模型{self.model_id}加载成功: {self.weight_path}")
            
        except Exception as e:
            mylog.error(f"模型初始化失败: {e}")
            raise
    
    def run(self, input_data, gain=None):
        """
        运行模型推理
        
        Args:
            input_data: 输入数据
            gain: 增益参数（可选）
            
        Returns:
            推理结果
        """
        try:
            if self.rknn is None:
                raise RuntimeError("模型未初始化")
            
            start_time = time.time()
            
            # 执行推理
            outputs = self.rknn.inference(inputs=[input_data])
            
            inference_time = time.time() - start_time
            mylog.debug(f"模型{self.model_id}推理耗时: {inference_time:.4f}秒")
            
            return outputs
            
        except Exception as e:
            mylog.error(f"模型推理失败: {e}")
            return None
    
    def get_model_info(self):
        """获取模型信息"""
        return {
            "model_id": self.model_id,
            "weight_name": self.weight_name,
            "weight_path": self.weight_path,
            "labels": self.labels,
            "is_loaded": self.rknn is not None
        }
    
    def release(self):
        """释放模型资源"""
        try:
            if self.rknn is not None:
                self.rknn.release()
                self.rknn = None
                mylog.info(f"模型{self.model_id}资源已释放")
        except Exception as e:
            mylog.error(f"释放模型资源失败: {e}")
    
    def __del__(self):
        """析构函数"""
        self.release()


class FaceModelManager:
    """人脸模型管理器"""
    
    def __init__(self):
        """初始化人脸模型管理器"""
        self.detection_model = None
        self.recognition_model = None
        self.models = {}
    
    def load_detection_model(self, model_name="retinaface_mob"):
        """加载人脸检测模型"""
        try:
            self.detection_model = ModelCheck(
                labels=["face"], 
                weight_name=model_name, 
                model_id=0
            )
            self.models["detection"] = self.detection_model
            mylog.info("人脸检测模型加载成功")
            return True
        except Exception as e:
            mylog.error(f"人脸检测模型加载失败: {e}")
            return False
    
    def load_recognition_model(self, model_name="face"):
        """加载人脸识别模型"""
        try:
            self.recognition_model = ModelCheck(
                labels=["feature"], 
                weight_name=model_name, 
                model_id=1
            )
            self.models["recognition"] = self.recognition_model
            mylog.info("人脸识别模型加载成功")
            return True
        except Exception as e:
            mylog.error(f"人脸识别模型加载失败: {e}")
            return False
    
    def get_detection_model(self):
        """获取人脸检测模型"""
        return self.detection_model
    
    def get_recognition_model(self):
        """获取人脸识别模型"""
        return self.recognition_model
    
    def release_all(self):
        """释放所有模型"""
        for model in self.models.values():
            if model:
                model.release()
        self.models.clear()
        self.detection_model = None
        self.recognition_model = None
        mylog.info("所有模型资源已释放")
    
    def get_models_info(self):
        """获取所有模型信息"""
        return {name: model.get_model_info() for name, model in self.models.items()}
    
    def __del__(self):
        """析构函数"""
        self.release_all()
