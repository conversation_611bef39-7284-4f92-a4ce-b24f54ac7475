import numpy as np
import cv2
from rknnlite.api import RKNNLite
from ...utils.log import mylog


class RKNNInference:
    """RKNN推理引擎"""
    
    def __init__(self, model_path, input_size=(640, 640)):
        """
        初始化RKNN推理引擎
        
        Args:
            model_path: RKNN模型文件路径
            input_size: 输入尺寸 (width, height)
        """
        self.model_path = model_path
        self.input_size = input_size
        self.rknn = None
        self.is_initialized = False
        
        self._init_model()
    
    def _init_model(self):
        """初始化模型"""
        try:
            self.rknn = RKNNLite()
            
            # 加载模型
            ret = self.rknn.load_rknn(self.model_path)
            if ret != 0:
                raise RuntimeError(f"Failed to load RKNN model: {self.model_path}")
            
            # 初始化运行时
            ret = self.rknn.init_runtime()
            if ret != 0:
                raise RuntimeError(f"Failed to initialize RKNN runtime: {self.model_path}")
            
            self.is_initialized = True
            mylog.info(f"RKNN模型初始化成功: {self.model_path}")
            
        except Exception as e:
            mylog.error(f"RKNN模型初始化失败: {e}")
            self.is_initialized = False
            raise
    
    def preprocess(self, image):
        """
        预处理输入图像
        
        Args:
            image: 输入图像 (numpy array)
            
        Returns:
            预处理后的图像
        """
        try:
            # 如果是BGR格式，转换为RGB
            if len(image.shape) == 3 and image.shape[2] == 3:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Resize到指定尺寸
            if image.shape[:2] != self.input_size[::-1]:  # (height, width)
                image = cv2.resize(image, self.input_size)
            
            # 归一化和数据类型转换
            image = image.astype(np.float32)
            
            # 添加batch维度
            if len(image.shape) == 3:
                image = np.expand_dims(image, axis=0)
            
            return image
            
        except Exception as e:
            mylog.error(f"图像预处理失败: {e}")
            return None
    
    def inference(self, input_data):
        """
        执行推理
        
        Args:
            input_data: 输入数据
            
        Returns:
            推理结果
        """
        try:
            if not self.is_initialized:
                raise RuntimeError("模型未初始化")
            
            # 执行推理
            outputs = self.rknn.inference(inputs=[input_data])
            return outputs
            
        except Exception as e:
            mylog.error(f"RKNN推理失败: {e}")
            return None
    
    def postprocess(self, outputs, conf_threshold=0.5, nms_threshold=0.4):
        """
        后处理推理结果
        
        Args:
            outputs: 模型输出
            conf_threshold: 置信度阈值
            nms_threshold: NMS阈值
            
        Returns:
            处理后的结果
        """
        try:
            if not outputs:
                return []
            
            # 这里需要根据具体模型的输出格式来实现
            # 以下是一个通用的示例实现
            results = []
            
            for output in outputs:
                if isinstance(output, np.ndarray):
                    # 处理检测结果
                    if len(output.shape) >= 2:
                        for detection in output:
                            if len(detection) >= 5 and detection[4] > conf_threshold:
                                results.append(detection.tolist())
            
            return results
            
        except Exception as e:
            mylog.error(f"后处理失败: {e}")
            return []
    
    def predict(self, image, conf_threshold=0.5, nms_threshold=0.4):
        """
        完整的预测流程
        
        Args:
            image: 输入图像
            conf_threshold: 置信度阈值
            nms_threshold: NMS阈值
            
        Returns:
            预测结果
        """
        try:
            # 预处理
            processed_image = self.preprocess(image)
            if processed_image is None:
                return []
            
            # 推理
            outputs = self.inference(processed_image)
            if outputs is None:
                return []
            
            # 后处理
            results = self.postprocess(outputs, conf_threshold, nms_threshold)
            
            return results
            
        except Exception as e:
            mylog.error(f"预测失败: {e}")
            return []
    
    def get_model_info(self):
        """获取模型信息"""
        return {
            "model_path": self.model_path,
            "input_size": self.input_size,
            "is_initialized": self.is_initialized
        }
    
    def release(self):
        """释放模型资源"""
        try:
            if self.rknn is not None:
                self.rknn.release()
                self.rknn = None
                self.is_initialized = False
                mylog.info(f"RKNN模型资源已释放: {self.model_path}")
        except Exception as e:
            mylog.error(f"释放RKNN模型资源失败: {e}")
    
    def __del__(self):
        """析构函数"""
        self.release()


class FaceDetectionRKNN(RKNNInference):
    """人脸检测RKNN推理引擎"""
    
    def __init__(self, model_path, input_size=(640, 640)):
        super().__init__(model_path, input_size)
    
    def preprocess(self, image):
        """人脸检测专用预处理"""
        try:
            # 转换颜色空间
            if len(image.shape) == 3 and image.shape[2] == 3:
                image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # Letterbox resize
            image = self._letterbox_resize(image, self.input_size)
            
            # 归一化
            image = image.astype(np.float32)
            image -= np.array((104, 117, 123), np.float32)
            
            # 添加batch维度
            image = np.expand_dims(image, axis=0)
            
            return image
            
        except Exception as e:
            mylog.error(f"人脸检测预处理失败: {e}")
            return None
    
    def _letterbox_resize(self, image, target_size):
        """Letterbox resize"""
        h, w = image.shape[:2]
        target_w, target_h = target_size
        
        # 计算缩放比例
        scale = min(target_w / w, target_h / h)
        new_w, new_h = int(w * scale), int(h * scale)
        
        # Resize
        resized = cv2.resize(image, (new_w, new_h))
        
        # 创建目标尺寸的图像
        result = np.full((target_h, target_w, 3), 128, dtype=np.uint8)
        
        # 计算粘贴位置
        x_offset = (target_w - new_w) // 2
        y_offset = (target_h - new_h) // 2
        
        # 粘贴
        result[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized
        
        return result


class FaceRecognitionRKNN(RKNNInference):
    """人脸识别RKNN推理引擎"""
    
    def __init__(self, model_path, input_size=(112, 112)):
        super().__init__(model_path, input_size)
    
    def preprocess(self, face_image):
        """人脸识别专用预处理"""
        try:
            # 确保是RGB格式
            if len(face_image.shape) == 3 and face_image.shape[2] == 3:
                # 假设输入是BGR，转换为RGB
                face_image = cv2.cvtColor(face_image, cv2.COLOR_BGR2RGB)
            
            # Resize到模型输入尺寸
            if face_image.shape[:2] != self.input_size[::-1]:
                face_image = cv2.resize(face_image, self.input_size)
            
            # 归一化到[-1, 1]
            face_image = face_image.astype(np.float32) / 255.0
            face_image = (face_image - 0.5) / 0.5
            
            # 转换维度顺序 (H, W, C) -> (C, H, W)
            face_image = np.transpose(face_image, (2, 0, 1))
            
            # 添加batch维度
            face_image = np.expand_dims(face_image, axis=0)
            
            return face_image
            
        except Exception as e:
            mylog.error(f"人脸识别预处理失败: {e}")
            return None
    
    def extract_feature(self, face_image):
        """提取人脸特征"""
        try:
            # 预处理
            processed_image = self.preprocess(face_image)
            if processed_image is None:
                return None
            
            # 推理
            outputs = self.inference(processed_image)
            if outputs is None or len(outputs) == 0:
                return None
            
            # 提取特征向量
            feature = np.array(outputs[0]).flatten()
            
            # L2归一化
            norm = np.linalg.norm(feature)
            if norm > 0:
                feature = feature / norm
            
            return feature
            
        except Exception as e:
            mylog.error(f"特征提取失败: {e}")
            return None
