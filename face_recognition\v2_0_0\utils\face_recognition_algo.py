from models.base_plugin import BasePlugin
import gc
import os
import numpy as np
from threading import Thread
from multiprocessing import Process, Queue
from .log import mylog
from ..inference.model_check import ModelCheck
from ..preprocess.face_preprocess import FacePreProcess
from ..postprocess.post_process import PostProcess
from ..task.image_alarm_interval import contrast_time
from .face_recognition_core import FaceRecognitionCore
from .i18n import I18n
import time
import importlib


class FaceRecognitionAlgo(BasePlugin):
    """
    人脸识别算法主类
    继承BasePlugin，实现完整的人脸识别算法接口
    """

    def __init__(self, model_name, model_version, algo_name):
        super().__init__(model_name, model_version, algo_name)
        self.post_mess = [model_name, model_version, algo_name]
        self.que = {}
        self.process_pid = {}
        self.kill_que = {}
        self.size = [640, 640]
        self.weight = "face_recognition"
        self.label = ['face', "person"]
        self.i18n = I18n()
        self.in_que = Queue(maxsize=5)
        self.out_que = Queue(maxsize=5)
        
        # 初始化人脸识别核心组件
        self._init_face_recognition_core()
        
        # 启动推理进程
        k = 3  # 并行推理进程数
        self.run_model = {}
        for i in range(k):
            self.run_model.update({i: Process(target=self._infer, args=(i, self.in_que, self.out_que), daemon=True)})
            self.run_model[i].start()
        
        # 启动结果处理线程
        back_model = Thread(target=self._get_message, daemon=True)
        back_model.start()
        
        mylog.info("人脸识别算法初始化完成")

    def _init_face_recognition_core(self):
        """初始化人脸识别核心组件"""
        try:
            # 获取模型路径 - face_info现在在face_recognition/v2_0_0/下
            # 直接使用相对于当前算法目录的face_info路径
            algo_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # face_recognition/v2_0_0/
            base_path = os.path.join(algo_dir, 'face_info')

            # 如果不存在，尝试其他可能的路径
            if not os.path.exists(base_path):
                # 尝试相对于当前工作目录
                current_dir = os.getcwd()
                base_path = os.path.join(current_dir, 'face_recognition', 'v2_0_0', 'face_info')

            # 如果还不存在，使用系统默认路径作为后备
            if not os.path.exists(base_path):
                base_path = '/userdata/lgw/model_repository/15000/chitu2-model-framework/models/face_info'

            face_model_path = os.path.join(base_path, 'weights', 'face.rknn')
            detection_model_path = os.path.join(base_path, 'weights', 'retinaface_mob.rknn')

            mylog.info(f"使用模型路径: {base_path}")
            mylog.info(f"人脸特征模型: {face_model_path}")
            mylog.info(f"人脸检测模型: {detection_model_path}")

            # 检查模型文件是否存在
            if not os.path.exists(face_model_path):
                raise FileNotFoundError(f"人脸特征模型文件不存在: {face_model_path}")
            if not os.path.exists(detection_model_path):
                raise FileNotFoundError(f"人脸检测模型文件不存在: {detection_model_path}")

            # 初始化人脸识别核心
            self.face_core = FaceRecognitionCore(
                face_model_path=face_model_path,
                detection_model_path=detection_model_path
            )

            mylog.info("人脸识别核心组件初始化成功")

        except Exception as e:
            mylog.error(f"人脸识别核心组件初始化失败: {e}")
            raise

    def __check_param(self, data):
        """检查参数"""
        if "recvDataType" not in data.get("businessData", {}).get("advsetValue", {}).keys():
            return False
        if not data.get("businessData", {}).get("image"):
            return False
        if data.get("businessData", {}).get("imageType") not in ["base64", "file", "numpy"]:
            return False
        return True
    
    def __url_check_param(self, data):
        """检查URL参数"""
        if "recvDataType" not in data.get("businessData", {}).get("advsetValue", {}).keys():
            return False
        if data.get("callBackData", {}).get("requestMethod") not in ["post", "POST"]:
            return False
        return True
            
    def change_language(self, language): 
        """切换语言"""
        back_message = self.i18n.reload(language)
        return back_message
        
    def infer(self, data, model_way):
        """推理接口"""
        if self.__check_param(data):
            if self.in_que.full():
                self.in_que.get()
            self.in_que.put([data, model_way])
            return True
        else:
            mylog.error("{'code': 500, 'msg': '传参错误，请参照接口文档重新传参。'}")
            return False
        
    def start_task(self, task_id, data):
        """启动任务"""
        try:
            if self.__url_check_param(data):
                self.que.update({task_id: Queue()})
                self.kill_que.update({task_id: Queue()})
                # 这里可以实现流式处理，暂时简化
                self.que[task_id].put(data)
                return 200
            else:
                mylog.error("{'code': 500, 'msg': '传参错误，请参照接口文档重新传参。'}")
                return 500
        except Exception as e:
            mylog.error(f"start task error: {e} \n")
            return 500

    def stop_task(self, task_id):
        """停止任务"""
        try:
            if task_id in self.kill_que:
                self.kill_que[task_id].put(True)
            if task_id in self.process_pid:
                if self.process_pid[task_id].is_alive():
                    self.process_pid[task_id].terminate()
                    gc.collect()
            if task_id in self.process_pid and not self.process_pid[task_id].is_alive():
                del self.process_pid[task_id]
                del self.kill_que[task_id]
                del self.que[task_id]
            return 200
        except Exception as e:
            mylog.error(f"stop task error: {e} \n")
            return 500
    
    def change_task(self, task_id, data):
        """更改任务"""
        try:
            if task_id in self.que:
                self.que[task_id].put(data)
            return 200
        except Exception as e:
            mylog.error(f"change task error: {e} \n")
            return 500
    
    def query_task(self, task_id=None):
        """查询任务"""
        back_task = {}
        if task_id is None: 
            for id in self.process_pid.keys():
                back_task[id] = self.process_pid[id].is_alive()
        elif task_id in self.process_pid.keys():
            back_task[task_id] = self.process_pid[task_id].is_alive()
        return back_task
        
    def _infer(self, number, in_que, out_que):
        """推理进程"""
        try:
            pre_process = FacePreProcess()
            post_process = PostProcess(self.i18n, self.post_mess)
            
            while True:
                try:
                    if not in_que.empty():
                        data, model_way = in_que.get()
                        
                        # 前处理
                        processed_img, source_img, gain = pre_process.get_data(data)

                        if processed_img is None or source_img is None:
                            back_json = post_process.format_error_response("图像处理失败")
                            out_que.put([back_json, False, data, model_way])
                            continue

                        # 获取检测模式
                        business_data = data.get("businessData", {})
                        advset_value = business_data.get("advsetValue", {})
                        recognition_mode = advset_value.get("faceRecognition", "all")
                        # 使用固定的相似度阈值，简化配置
                        similarity_threshold = 0.15

                        # 根据模式进行不同的处理
                        if recognition_mode == "all":
                            # all模式：只检测人脸，不对比人脸库
                            mylog.info("all模式：执行人脸检测")
                            img, or_img, boxes = self.face_core.detect_faces(source_img)

                            mylog.info(f"检测到 {len(boxes)} 个人脸框")

                            if boxes:
                                # 构造简单的人脸结果（不包含身份信息）
                                faces = []
                                for box in boxes:
                                    b = list(map(float, box))
                                    x1, y1, x2, y2 = b[:4]
                                    confidence = b[4] if len(b) > 4 else 0.9

                                    # 处理landmarks
                                    if len(b) >= 15:
                                        landmarks = [[b[5], b[6]], [b[7], b[8]], [b[9], b[10]], [b[11], b[12]], [b[13], b[14]]]
                                    else:
                                        # 默认关键点
                                        w, h = x2 - x1, y2 - y1
                                        landmarks = [
                                            [x1 + w * 0.3, y1 + h * 0.4],  # 左眼
                                            [x1 + w * 0.7, y1 + h * 0.4],  # 右眼
                                            [x1 + w * 0.5, y1 + h * 0.6],  # 鼻子
                                            [x1 + w * 0.35, y1 + h * 0.8], # 左嘴角
                                            [x1 + w * 0.65, y1 + h * 0.8]  # 右嘴角
                                        ]

                                    faces.append({
                                        "bbox": [int(x1), int(y1), int(x2), int(y2)],
                                        "confidence": float(confidence),
                                        "landmarks": landmarks,
                                        "status": "detected"  # all模式下只标记为检测到
                                    })

                                recognition_result = {
                                    "status": "success",
                                    "message": f"检测到{len(faces)}张人脸",
                                    "faces": faces,
                                    "face_count": len(faces),
                                    "known_count": 0,
                                    "stranger_count": 0
                                }
                                mylog.info(f"all模式：成功检测到{len(faces)}张人脸，将触发报警")
                            else:
                                recognition_result = {
                                    "status": "no_face",
                                    "message": "未检测到人脸",
                                    "faces": [],
                                    "face_count": 0
                                }
                                mylog.info("all模式：未检测到人脸，不触发报警")
                        else:
                            # known_face或stranger_detection模式：需要对比人脸库
                            mylog.info(f"{recognition_mode}模式：执行人脸识别")
                            recognition_result = self.face_core.recognize_face(
                                source_img,
                                similarity_threshold=similarity_threshold
                            )

                        # 后处理
                        back_json, post_bool = post_process.run_process(data, recognition_result, source_img)
                        out_que.put([back_json, post_bool, data, model_way])
                        
                    else:
                        time.sleep(0.01)
                        
                except Exception as e:
                    mylog.error(f"推理进程 {number} 错误: {e}")
                    back_json = post_process.format_error_response(f"推理失败: {e}")
                    out_que.put([back_json, False, data, model_way])
                    
        except Exception as e:
            mylog.error(f"推理进程 {number} 初始化失败: {e}")
    
    def _get_message(self):
        """获取消息处理线程"""
        interval_image = {}
        while True:
            try:
                if not self.out_que.empty():
                    back_json, post_bool, data, model_way = self.out_que.get()
                    
                    business_data = data["businessData"]
                    
                    # 处理间隔控制
                    if 'interval' in business_data['advsetValue']:
                        transaction_number = data['transactionNumber']
                        interval = business_data['advsetValue']['interval']
                        
                        if post_bool:
                            alarm_bool, interval_image = contrast_time(interval_image, transaction_number, interval)
                            if alarm_bool:
                                back_json["transactionNumber"] = transaction_number
                                importlib.import_module(model_way).get_model_back(back_json, data)
                            else:
                                importlib.import_module(model_way).get_model_back(None, data)
                        else:
                            importlib.import_module(model_way).get_model_back(None, data)
                    else:
                        importlib.import_module(model_way).get_model_back(back_json, data)
                        
                time.sleep(0.01)
                
            except Exception as e:
                mylog.error(f"消息处理失败: {e}")
    
    def add_face(self, img_input, person_id: str, lib_id: str, name: str):
        """添加人脸到库"""
        try:
            result_id = self.face_core.add_face_to_index(img_input, person_id, lib_id, name)
            if result_id is not None:
                return {"code": 200, "message": "添加成功", "face_id": result_id}
            else:
                return {"code": 500, "message": "添加失败"}
        except Exception as e:
            mylog.error(f"添加人脸失败: {e}")
            return {"code": 500, "message": f"添加失败: {e}"}
    
    def delete_face(self, face_id: int):
        """删除人脸"""
        try:
            success = self.face_core.delete_face_from_index(face_id)
            if success:
                return {"code": 200, "message": "删除成功"}
            else:
                return {"code": 500, "message": "删除失败"}
        except Exception as e:
            mylog.error(f"删除人脸失败: {e}")
            return {"code": 500, "message": f"删除失败: {e}"}
    
    def get_face_info(self):
        """获取人脸库信息"""
        try:
            faces = self.face_core.get_all_faces()
            count = self.face_core.get_face_count()
            return {
                "code": 200, 
                "message": "获取成功",
                "count": count,
                "faces": faces
            }
        except Exception as e:
            mylog.error(f"获取人脸信息失败: {e}")
            return {"code": 500, "message": f"获取失败: {e}"}
    
    def clear_face_library(self):
        """清空人脸库"""
        try:
            success = self.face_core.clear_index()
            if success:
                return {"code": 200, "message": "清空成功"}
            else:
                return {"code": 500, "message": "清空失败"}
        except Exception as e:
            mylog.error(f"清空人脸库失败: {e}")
            return {"code": 500, "message": f"清空失败: {e}"}
