from models.base_plugin import BasePlugin
import gc
import os
from threading import Thread
from multiprocessing import Process, Queue
from .log import mylog
from ..inference.model_check import ModelCheck
from ..preprocess.face_preprocess import FacePreProcess
from ..postprocess.post_process import PostProcess
from ..task.image_alarm_interval import contrast_time
from .face_recognition_core import FaceRecognitionCore
from .i18n import I18n
import time
import importlib


class FaceRecognitionAlgo(BasePlugin):
    """
    人脸识别算法主类
    继承BasePlugin，实现完整的人脸识别算法接口
    """

    def __init__(self, model_name, model_version, algo_name):
        super().__init__(model_name, model_version, algo_name)
        self.post_mess = [model_name, model_version, algo_name]
        self.que = {}
        self.process_pid = {}
        self.kill_que = {}
        self.size = [640, 640]
        self.weight = "face_recognition"
        self.label = ['face', "person"]
        self.i18n = I18n()
        self.in_que = Queue(maxsize=5)
        self.out_que = Queue(maxsize=5)
        
        # 初始化人脸识别核心组件
        self._init_face_recognition_core()
        
        # 启动推理进程
        k = 3  # 并行推理进程数
        self.run_model = {}
        for i in range(k):
            self.run_model.update({i: Process(target=self._infer, args=(i, self.in_que, self.out_que), daemon=True)})
            self.run_model[i].start()
        
        # 启动结果处理线程
        back_model = Thread(target=self._get_message, daemon=True)
        back_model.start()
        
        mylog.info("人脸识别算法初始化完成")

    def _init_face_recognition_core(self):
        """初始化人脸识别核心组件"""
        try:
            # 获取模型路径
            base_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), 'face_info')
            face_model_path = os.path.join(base_path, 'weights', 'face.rknn')
            detection_model_path = os.path.join(base_path, 'weights', 'retinaface_mob.rknn')
            
            # 初始化人脸识别核心
            self.face_core = FaceRecognitionCore(
                face_model_path=face_model_path,
                detection_model_path=detection_model_path
            )
            
            mylog.info("人脸识别核心组件初始化成功")
            
        except Exception as e:
            mylog.error(f"人脸识别核心组件初始化失败: {e}")
            raise

    def __check_param(self, data):
        """检查参数"""
        if "recvDataType" not in data.get("businessData", {}).get("advsetValue", {}).keys():
            return False
        if not data.get("businessData", {}).get("image"):
            return False
        if data.get("businessData", {}).get("imageType") not in ["base64", "file", "numpy"]:
            return False
        return True
    
    def __url_check_param(self, data):
        """检查URL参数"""
        if "recvDataType" not in data.get("businessData", {}).get("advsetValue", {}).keys():
            return False
        if data.get("callBackData", {}).get("requestMethod") not in ["post", "POST"]:
            return False
        return True
            
    def change_language(self, language): 
        """切换语言"""
        back_message = self.i18n.reload(language)
        return back_message
        
    def infer(self, data, model_way):
        """推理接口"""
        if self.__check_param(data):
            if self.in_que.full():
                self.in_que.get()
            self.in_que.put([data, model_way])
            return True
        else:
            mylog.error("{'code': 500, 'msg': '传参错误，请参照接口文档重新传参。'}")
            return False
        
    def start_task(self, task_id, data):
        """启动任务"""
        try:
            if self.__url_check_param(data):
                self.que.update({task_id: Queue()})
                self.kill_que.update({task_id: Queue()})
                # 这里可以实现流式处理，暂时简化
                self.que[task_id].put(data)
                return 200
            else:
                mylog.error("{'code': 500, 'msg': '传参错误，请参照接口文档重新传参。'}")
                return 500
        except Exception as e:
            mylog.error(f"start task error: {e} \n")
            return 500

    def stop_task(self, task_id):
        """停止任务"""
        try:
            if task_id in self.kill_que:
                self.kill_que[task_id].put(True)
            if task_id in self.process_pid:
                if self.process_pid[task_id].is_alive():
                    self.process_pid[task_id].terminate()
                    gc.collect()
            if task_id in self.process_pid and not self.process_pid[task_id].is_alive():
                del self.process_pid[task_id]
                del self.kill_que[task_id]
                del self.que[task_id]
            return 200
        except Exception as e:
            mylog.error(f"stop task error: {e} \n")
            return 500
    
    def change_task(self, task_id, data):
        """更改任务"""
        try:
            if task_id in self.que:
                self.que[task_id].put(data)
            return 200
        except Exception as e:
            mylog.error(f"change task error: {e} \n")
            return 500
    
    def query_task(self, task_id=None):
        """查询任务"""
        back_task = {}
        if task_id is None: 
            for id in self.process_pid.keys():
                back_task[id] = self.process_pid[id].is_alive()
        elif task_id in self.process_pid.keys():
            back_task[task_id] = self.process_pid[task_id].is_alive()
        return back_task
        
    def _infer(self, number, in_que, out_que):
        """推理进程"""
        try:
            pre_process = FacePreProcess()
            post_process = PostProcess(self.i18n, self.post_mess)
            
            while True:
                try:
                    if not in_que.empty():
                        data, model_way = in_que.get()
                        
                        # 前处理
                        processed_img, source_img, gain = pre_process.get_data(data)
                        
                        if processed_img is None or source_img is None:
                            back_json = post_process.format_error_response("图像处理失败")
                            out_que.put([back_json, False, data, model_way])
                            continue
                        
                        # 人脸识别
                        recognition_result = self.face_core.recognize_face(
                            source_img, 
                            similarity_threshold=data.get("businessData", {}).get("advsetValue", {}).get("similarityThreshold", 0.15)
                        )
                        
                        # 后处理
                        back_json, post_bool = post_process.run_process(data, recognition_result, source_img)
                        out_que.put([back_json, post_bool, data, model_way])
                        
                    else:
                        time.sleep(0.01)
                        
                except Exception as e:
                    mylog.error(f"推理进程 {number} 错误: {e}")
                    back_json = post_process.format_error_response(f"推理失败: {e}")
                    out_que.put([back_json, False, data, model_way])
                    
        except Exception as e:
            mylog.error(f"推理进程 {number} 初始化失败: {e}")
    
    def _get_message(self):
        """获取消息处理线程"""
        interval_image = {}
        while True:
            try:
                if not self.out_que.empty():
                    back_json, post_bool, data, model_way = self.out_que.get()
                    
                    business_data = data["businessData"]
                    
                    # 处理间隔控制
                    if 'interval' in business_data['advsetValue']:
                        transaction_number = data['transactionNumber']
                        interval = business_data['advsetValue']['interval']
                        
                        if post_bool:
                            alarm_bool, interval_image = contrast_time(interval_image, transaction_number, interval)
                            if alarm_bool:
                                back_json["transactionNumber"] = transaction_number
                                importlib.import_module(model_way).get_model_back(back_json, data)
                            else:
                                importlib.import_module(model_way).get_model_back(None, data)
                        else:
                            importlib.import_module(model_way).get_model_back(None, data)
                    else:
                        importlib.import_module(model_way).get_model_back(back_json, data)
                        
                time.sleep(0.01)
                
            except Exception as e:
                mylog.error(f"消息处理失败: {e}")
    
    def add_face(self, img_input, person_id: str, lib_id: str, name: str):
        """添加人脸到库"""
        try:
            result_id = self.face_core.add_face_to_index(img_input, person_id, lib_id, name)
            if result_id is not None:
                return {"code": 200, "message": "添加成功", "face_id": result_id}
            else:
                return {"code": 500, "message": "添加失败"}
        except Exception as e:
            mylog.error(f"添加人脸失败: {e}")
            return {"code": 500, "message": f"添加失败: {e}"}
    
    def delete_face(self, face_id: int):
        """删除人脸"""
        try:
            success = self.face_core.delete_face_from_index(face_id)
            if success:
                return {"code": 200, "message": "删除成功"}
            else:
                return {"code": 500, "message": "删除失败"}
        except Exception as e:
            mylog.error(f"删除人脸失败: {e}")
            return {"code": 500, "message": f"删除失败: {e}"}
    
    def get_face_info(self):
        """获取人脸库信息"""
        try:
            faces = self.face_core.get_all_faces()
            count = self.face_core.get_face_count()
            return {
                "code": 200, 
                "message": "获取成功",
                "count": count,
                "faces": faces
            }
        except Exception as e:
            mylog.error(f"获取人脸信息失败: {e}")
            return {"code": 500, "message": f"获取失败: {e}"}
    
    def clear_face_library(self):
        """清空人脸库"""
        try:
            success = self.face_core.clear_index()
            if success:
                return {"code": 200, "message": "清空成功"}
            else:
                return {"code": 500, "message": "清空失败"}
        except Exception as e:
            mylog.error(f"清空人脸库失败: {e}")
            return {"code": 500, "message": f"清空失败: {e}"}
