# 人脸识别算法 (Face Recognition Algorithm)

基于深度学习的人脸识别与身份验证算法，支持已知人脸识别、陌生人检测等多种模式。

## 功能特性

- **多种识别模式**: 支持已知人脸识别、陌生人检测、全部模式
- **高效检索**: 基于Faiss的高效人脸特征检索
- **嵌入式支持**: 支持RKNN模型推理，适配嵌入式设备
- **可配置参数**: 支持相似度阈值、识别间隔等参数配置
- **多种输出格式**: 支持推理结果、绘制图片、人脸信息等多种输出
- **国际化支持**: 支持中英文语言切换

## 目录结构

```
face_recognition/
├── v2_0_0/
│   ├── config/                 # 配置文件
│   │   ├── documentation.json  # 算法文档配置
│   │   └── update.json        # 更新信息
│   ├── inference/             # 推理模块
│   │   ├── model_check.py     # 模型检查
│   │   └── inference_rknn/    # RKNN推理
│   ├── preprocess/            # 预处理模块
│   │   └── face_preprocess.py # 人脸预处理
│   ├── postprocess/           # 后处理模块
│   │   └── post_process.py    # 结果后处理
│   ├── task/                  # 任务模块
│   │   └── image_alarm_interval.py # 间隔控制
│   ├── utils/                 # 工具模块
│   │   ├── face_recognition_core.py    # 核心功能
│   │   ├── face_recognition_algo.py    # 主算法类
│   │   ├── i18n.py           # 国际化
│   │   └── log.py            # 日志
│   ├── i18n/                 # 国际化文件
│   │   ├── zh.json           # 中文
│   │   └── en.json           # 英文
│   ├── weights/              # 权重文件目录
│   └── main.py               # 主入口文件
└── README.md                 # 说明文档
```

## 安装要求

- Python >= 3.7
- opencv-python
- numpy
- faiss-cpu
- rknnlite
- pillow

## 模型文件

算法需要以下模型文件，应放置在 `face_info/weights/` 目录下：

1. `face.rknn` - 人脸特征提取模型
2. `retinaface_mob.rknn` - 人脸检测模型

## 使用方法

### 1. 基本使用

```python
from face_recognition.v2_0_0.main import load

# 加载算法
algorithms = load()
algo = list(algorithms.values())[0]

# 推理
data = {
    "businessData": {
        "image": "base64_image_string",
        "imageType": "base64",
        "advsetValue": {
            "recognitionMode": "known_face",
            "similarityThreshold": 0.15,
            "recvDataType": ["infer_results", "draw_image"]
        }
    }
}

result = algo.infer(data, "model_way")
```

### 2. 人脸库管理

```python
# 添加人脸
result = algo.add_face(image_path, "person_001", "lib_001", "张三")

# 删除人脸
result = algo.delete_face(face_id)

# 获取人脸库信息
info = algo.get_face_info()

# 清空人脸库
result = algo.clear_face_library()
```

### 3. 配置参数

主要配置参数：

- `recognitionMode`: 识别模式
  - `known_face`: 已知人脸识别
  - `stranger_detection`: 陌生人检测
  - `all`: 全部模式

- `similarityThreshold`: 相似度阈值 (0.0-1.0)
- `interval`: 识别间隔时间（秒）
- `maxFaceSize`: 最大人脸尺寸
- `recvDataType`: 输出数据类型
  - `infer_results`: 推理结果
  - `draw_image`: 绘制后图片
  - `source_image`: 原始图片
  - `face_info`: 人脸信息

## API接口

### 推理接口

```python
def infer(data, model_way):
    """
    人脸识别推理
    
    Args:
        data: 包含图像和配置的数据字典
        model_way: 模型方式
        
    Returns:
        bool: 是否成功提交推理任务
    """
```

### 任务管理

```python
def start_task(task_id, data):
    """启动任务"""
    
def stop_task(task_id):
    """停止任务"""
    
def change_task(task_id, data):
    """更改任务配置"""
    
def query_task(task_id=None):
    """查询任务状态"""
```

### 语言切换

```python
def change_language(language):
    """
    切换语言
    
    Args:
        language: 'zh' 或 'en'
    """
```

## 输出格式

### 成功响应

```json
{
    "modelName": "face_recognition",
    "modelVersion": "v2.0.0",
    "algoName": "face_recognition",
    "timestamp": 1627890123456,
    "status": "success",
    "message": "检测到1张人脸",
    "faceCount": 1,
    "inferResults": [
        {
            "bbox": [100, 100, 200, 200],
            "confidence": 0.95,
            "landmarks": [[110, 120], [190, 120], [150, 150], [130, 180], [170, 180]],
            "status": "known",
            "personId": "person_001",
            "name": "张三",
            "similarity": 0.85
        }
    ],
    "drawImage": "data:image/jpeg;base64,/9j/4AAQ...",
    "faceInfo": {
        "knownFaces": [...],
        "strangerFaces": [...],
        "totalCount": 1,
        "knownCount": 1,
        "strangerCount": 0
    }
}
```

### 错误响应

```json
{
    "modelName": "face_recognition",
    "modelVersion": "v2.0.0",
    "algoName": "face_recognition",
    "timestamp": 1627890123456,
    "status": "error",
    "errorCode": 500,
    "message": "图像处理失败",
    "faceCount": 0,
    "inferResults": []
}
```

## 注意事项

1. 确保模型文件路径正确
2. 输入图像质量要求：清晰、光照良好
3. 人脸尺寸建议大于64x64像素
4. 相似度阈值需要根据实际场景调整
5. 定期清理过期的间隔记录以节省内存

## 版本历史

### v2.0.0 (2025-07-29)
- 初始版本发布
- 支持多种识别模式
- 基于Faiss的高效检索
- RKNN模型支持
- 国际化支持
