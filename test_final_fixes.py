#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证最终修复的测试脚本
"""

import os
import json

def test_face_info_path():
    """测试face_info路径"""
    print("=" * 50)
    print("测试face_info路径...")
    
    # 检查新的face_info路径
    face_info_path = "face_recognition/v2_0_0/face_info"
    
    if os.path.exists(face_info_path):
        print(f"✓ face_info目录存在: {face_info_path}")
        
        # 检查子目录
        subdirs = ["weights", "face_files", "face_images", "stranger_images"]
        for subdir in subdirs:
            subdir_path = os.path.join(face_info_path, subdir)
            if os.path.exists(subdir_path):
                print(f"✓ 子目录存在: {subdir}")
            else:
                print(f"✗ 子目录缺失: {subdir}")
                return False
        
        # 检查权重文件
        weights_dir = os.path.join(face_info_path, "weights")
        required_weights = ["face.rknn", "retinaface_mob.rknn"]
        
        for weight_file in required_weights:
            weight_path = os.path.join(weights_dir, weight_file)
            if os.path.exists(weight_path):
                size = os.path.getsize(weight_path)
                print(f"✓ 权重文件存在: {weight_file} ({size} bytes)")
            else:
                print(f"✗ 权重文件缺失: {weight_file}")
                return False
        
        return True
    else:
        print(f"✗ face_info目录不存在: {face_info_path}")
        return False

def test_frontend_config():
    """测试前端配置"""
    print("=" * 50)
    print("测试前端配置...")
    
    try:
        config_path = "face_recognition/v2_0_0/config/documentation.json"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查基本信息
        print(f"✓ 模型名称: {config['modelName']}")
        print(f"✓ 模型版本: {config['modelVersion']}")
        
        # 检查算法配置
        algo_config = config["algoSet"][0]
        print(f"✓ 算法名称: {algo_config['algoName']}")
        
        # 检查前端配置字段
        advset_value = algo_config["param"]["businessData"]["advsetValue"]
        
        required_fields = {
            "area": "画框",
            "interval": "报警时间间隔", 
            "recognitionMode": "检测方式",
            "recvDataType": "结果输出类型"
        }
        
        for field_name, field_desc in required_fields.items():
            if field_name in advset_value:
                field_config = advset_value[field_name]
                if field_name == "area":
                    # 检查画框配置
                    area_type = field_config.get("areaType", {})
                    if area_type.get("label", {}).get("zh") == "画框":
                        print(f"✓ {field_desc}配置正确")
                    else:
                        print(f"✗ {field_desc}配置错误")
                        return False
                elif field_name == "interval":
                    # 检查报警间隔配置
                    if field_config.get("label", {}).get("zh") == "报警时间间隔":
                        print(f"✓ {field_desc}配置正确")
                        print(f"  - 默认值: {field_config.get('value')}秒")
                        print(f"  - 范围: {field_config.get('min', 1)}-{field_config.get('max', 3600)}")
                    else:
                        print(f"✗ {field_desc}配置错误")
                        return False
                elif field_name == "recognitionMode":
                    # 检查检测方式配置
                    if field_config.get("label", {}).get("zh") == "检测方式":
                        options = field_config.get("options", [])
                        option_values = [opt["value"] for opt in options]
                        expected_modes = ["all", "known_face", "stranger_detection"]
                        
                        if all(mode in option_values for mode in expected_modes):
                            print(f"✓ {field_desc}配置正确")
                            print(f"  - 选项: {[opt['label']['zh'] for opt in options]}")
                        else:
                            print(f"✗ {field_desc}选项不完整")
                            return False
                    else:
                        print(f"✗ {field_desc}配置错误")
                        return False
                elif field_name == "recvDataType":
                    # 检查结果输出类型配置
                    if field_config.get("label", {}).get("zh") == "结果输出类型":
                        options = field_config.get("options", [])
                        option_values = [opt["value"] for opt in options]
                        expected_types = ["infer_results", "draw_image", "source_image", "face_info"]
                        
                        if all(output_type in option_values for output_type in expected_types):
                            print(f"✓ {field_desc}配置正确")
                            print(f"  - 选项: {[opt['label']['zh'] for opt in options]}")
                        else:
                            print(f"✗ {field_desc}选项不完整")
                            return False
                    else:
                        print(f"✗ {field_desc}配置错误")
                        return False
            else:
                print(f"✗ 缺少配置字段: {field_name}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 前端配置测试失败: {e}")
        return False

def test_algorithm_structure():
    """测试算法结构"""
    print("=" * 50)
    print("测试算法结构...")
    
    base_dir = "face_recognition/v2_0_0"
    
    # 检查核心文件
    core_files = [
        "main.py",
        "config/documentation.json",
        "utils/face_recognition_algo.py",
        "utils/face_recognition_core.py",
        "preprocess/face_preprocess.py",
        "postprocess/post_process.py",
        "inference/model_check.py"
    ]
    
    for file_path in core_files:
        full_path = os.path.join(base_dir, file_path)
        if os.path.exists(full_path):
            print(f"✓ 核心文件存在: {file_path}")
        else:
            print(f"✗ 核心文件缺失: {file_path}")
            return False
    
    # 检查face_info结构
    face_info_structure = [
        "face_info/weights",
        "face_info/face_files", 
        "face_info/face_images",
        "face_info/stranger_images"
    ]
    
    for dir_path in face_info_structure:
        full_path = os.path.join(base_dir, dir_path)
        if os.path.exists(full_path):
            print(f"✓ face_info结构存在: {dir_path}")
        else:
            print(f"✗ face_info结构缺失: {dir_path}")
            return False
    
    return True

def main():
    """主测试函数"""
    print("验证最终修复")
    print("=" * 60)
    
    tests = [
        ("算法结构", test_algorithm_structure),
        ("face_info路径", test_face_info_path),
        ("前端配置", test_frontend_config),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有修复验证通过！")
        
        print("\n最终修复总结:")
        print("✅ 1. 修改了face_info路径到face_recognition/v2_0_0/face_info")
        print("✅ 2. 重新设计了前端配置字段:")
        print("   - 画框 (area)")
        print("   - 报警时间间隔 (interval)")
        print("   - 检测方式 (recognitionMode)")
        print("   - 结果输出类型 (recvDataType)")
        print("✅ 3. 优化了配置字段的显示和标签")
        print("✅ 4. 完善了算法目录结构")
        
        print("\n前端配置说明:")
        print("- 画框: 支持多边形区域选择")
        print("- 报警时间间隔: 1-3600秒，默认30秒")
        print("- 检测方式: 全部/已知人脸/陌生人")
        print("- 结果输出类型: 推理结果/绘制图片/原始图片/人脸信息")
        
        return True
    else:
        print("❌ 部分修复验证失败")
        return False

if __name__ == "__main__":
    main()
