#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局结果上报模块
处理算法检测结果的上报
"""

import json
import requests
import traceback
from log import mylog


def get_model_back(back_json, data):
    """
    将算法检测结果上报给调用方
    
    Args:
        back_json: 检测结果数据，如果为None表示无需上报
        data: 原始请求数据
    """
    try:
        if back_json is None:
            mylog.debug("算法检测：无检测结果，跳过上报")
            return
        
        # 获取回调URL
        business_data = data.get("businessData", {})
        callback_url = business_data.get("callbackUrl")
        
        if not callback_url:
            mylog.warning("算法检测：未提供回调URL，无法上报结果")
            return
        
        # 准备上报数据
        report_data = {
            "transactionNumber": back_json.get("transactionNumber", data.get("transactionNumber", "")),
            "timestamp": back_json.get("timestamp"),
            "modelName": back_json.get("modelName"),
            "modelVersion": back_json.get("modelVersion"),
            "algoName": back_json.get("algoName"),
            "status": back_json.get("status"),
            "message": back_json.get("message"),
            "faceCount": back_json.get("faceCount", 0),
            "inferResults": back_json.get("inferResults", []),
            "drawImage": back_json.get("drawImage"),
            "sourceImage": back_json.get("sourceImage")
        }
        
        # 移除None值
        report_data = {k: v for k, v in report_data.items() if v is not None}
        
        mylog.info(f"算法检测：准备上报结果到 {callback_url}")
        mylog.debug(f"上报数据: {json.dumps(report_data, ensure_ascii=False, indent=2)}")
        
        # 发送POST请求
        response = requests.post(
            callback_url,
            json=report_data,
            timeout=10,
            headers={'Content-Type': 'application/json'}
        )
        
        if response.status_code == 200:
            mylog.info("算法检测：检测结果上报成功")
        else:
            mylog.error(f"算法检测：检测结果上报失败，状态码: {response.status_code}, 响应: {response.text}")
            
    except requests.exceptions.Timeout:
        mylog.error("算法检测：检测结果上报失败 - 请求超时")
    except requests.exceptions.ConnectionError:
        mylog.error("算法检测：检测结果上报失败 - 连接错误")
    except Exception as e:
        mylog.error(f"算法检测：检测结果上报失败 - {e}")
        mylog.error(f"详细错误: {traceback.format_exc()}")


if __name__ == "__main__":
    # 测试函数
    test_data = {
        "transactionNumber": "test-123",
        "businessData": {
            "callbackUrl": "http://localhost:8080/callback"
        }
    }
    
    test_result = {
        "transactionNumber": "test-123",
        "timestamp": 1690000000000,
        "modelName": "face_recognition",
        "status": "success",
        "message": "检测到1张人脸",
        "faceCount": 1
    }
    
    print("测试上报功能...")
    get_model_back(test_result, test_data)
