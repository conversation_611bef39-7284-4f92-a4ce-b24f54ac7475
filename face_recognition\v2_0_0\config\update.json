{"version": "v2.0.0", "updateTime": "2025-07-29", "description": {"zh": "人脸识别算法v2.0.0版本，支持已知人脸识别、陌生人检测等功能", "en": "Face recognition algorithm v2.0.0, supports known face recognition, stranger detection and other functions"}, "features": [{"zh": "支持多种识别模式：已知人脸识别、陌生人检测、全部模式", "en": "Support multiple recognition modes: known face recognition, stranger detection, all mode"}, {"zh": "基于Faiss的高效人脸特征检索", "en": "Efficient face feature retrieval based on Faiss"}, {"zh": "支持RKNN模型推理，适配嵌入式设备", "en": "Support RKNN model inference, compatible with embedded devices"}, {"zh": "可配置的相似度阈值和识别间隔", "en": "Configurable similarity threshold and recognition interval"}, {"zh": "支持多种输出格式：推理结果、绘制图片、人脸信息等", "en": "Support multiple output formats: inference results, drawn images, face information, etc."}], "requirements": {"python": ">=3.7", "dependencies": ["opencv-python", "numpy", "faiss-cpu", "rknnlite", "pillow"]}, "modelFiles": [{"name": "face.rknn", "description": {"zh": "人脸特征提取模型", "en": "Face feature extraction model"}, "path": "weights/face.rknn"}, {"name": "retinaface_mob.rknn", "description": {"zh": "人脸检测模型", "en": "Face detection model"}, "path": "weights/retinaface_mob.rknn"}]}