#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试人脸识别API调用
"""

import requests
import json
import base64
import time

def create_test_image_base64():
    """创建一个简单的测试图片的base64编码"""
    # 这是一个1x1像素的透明PNG图片的base64编码
    return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="

def test_face_recognition_api():
    """测试人脸识别API"""
    print("测试人脸识别API调用")
    print("=" * 50)
    
    # API端点
    api_url = "http://*************:15000/ai/infer"
    
    # 回调URL（模拟的）
    callback_url = "http://*************:8080/callback"
    
    # 构造请求数据
    request_data = {
        "transactionNumber": f"test-{int(time.time())}",
        "callBackData": "test_callback_data",
        "businessData": {
            "image": create_test_image_base64(),
            "imageType": "base64",
            "imageId": "test_image_001",
            "callbackUrl": callback_url,  # 关键：提供回调URL
            "advsetValue": {
                "area": {
                    "areaType": "POLYGON",
                    "positions": [[0, 0], [100, 0], [100, 100], [0, 100]]
                },
                "interval": 1,  # 1秒间隔
                "faceRecognition": "all",  # all模式
                "recvDataType": ["infer_results", "draw_image", "source_image"]
            }
        }
    }
    
    print("📋 请求数据:")
    print(f"- API URL: {api_url}")
    print(f"- 回调URL: {callback_url}")
    print(f"- 检测模式: all")
    print(f"- 间隔时间: 1秒")
    print(f"- 事务编号: {request_data['transactionNumber']}")
    
    try:
        print("\n🚀 发送API请求...")
        response = requests.post(
            api_url,
            json=request_data,
            timeout=30,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API调用成功!")
            print(f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
            print("\n🔍 现在检查日志文件...")
            print("应该能看到:")
            print("1. 算法检测：开始处理结果上报")
            print("2. back_json类型和data类型信息")
            print("3. 详细的回调URL信息")
            print("4. 上报成功或失败的具体原因")
            
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败: 无法连接到API服务")
        print("请确认:")
        print("1. 服务是否正在运行 (python3 app.py)")
        print("2. 端口15000是否可访问")
        print("3. IP地址是否正确")
        
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        
    except Exception as e:
        print(f"❌ 请求异常: {e}")

def setup_callback_server():
    """设置一个简单的回调服务器来接收上报"""
    print("\n设置回调服务器")
    print("=" * 30)
    
    callback_code = '''
from flask import Flask, request, jsonify
import json

app = Flask(__name__)

@app.route('/callback', methods=['POST'])
def callback():
    print("🎉 收到回调上报!")
    data = request.get_json()
    print(f"上报数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
    return jsonify({"status": "success", "message": "回调接收成功"})

if __name__ == '__main__':
    print("启动回调服务器在 http://*************:8080")
    app.run(host='0.0.0.0', port=8080, debug=True)
'''
    
    print("💡 要完整测试回调功能，需要启动一个回调服务器:")
    print("1. 创建 callback_server.py 文件")
    print("2. 复制上面的代码")
    print("3. 运行: python3 callback_server.py")
    print("4. 然后运行这个测试脚本")
    
    with open('callback_server.py', 'w', encoding='utf-8') as f:
        f.write(callback_code)
    
    print("✅ 已创建 callback_server.py 文件")

def main():
    """主函数"""
    print("人脸识别API测试工具")
    print("解决回调上报问题")
    
    # 设置回调服务器
    setup_callback_server()
    
    print("\n" + "=" * 50)
    input("按回车键开始API测试 (确保服务正在运行)...")
    
    # 测试API调用
    test_face_recognition_api()
    
    print("\n" + "=" * 50)
    print("🎯 测试完成!")
    print("\n下一步:")
    print("1. 检查最新的日志文件 2025-07-29.log")
    print("2. 查找新的调试信息")
    print("3. 确认回调上报是否成功")
    
    print("\n💡 如果要测试完整的回调流程:")
    print("1. 在另一个终端运行: python3 callback_server.py")
    print("2. 然后重新运行这个测试")

if __name__ == "__main__":
    main()
