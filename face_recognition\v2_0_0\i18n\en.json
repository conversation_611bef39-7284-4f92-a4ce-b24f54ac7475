{"algorithm_name": "Face Recognition Algorithm", "algorithm_description": "Deep learning based face recognition and identity verification algorithm", "messages": {"face_detected": "Face detected", "face_recognized": "Known face recognized", "stranger_detected": "Stranger detected", "no_face_detected": "No face detected", "face_too_small": "Face size too small", "face_too_blurry": "Face too blurry", "multiple_faces": "Multiple faces detected", "recognition_success": "Face recognition successful", "recognition_failed": "Face recognition failed", "feature_extraction_failed": "Feature extraction failed", "model_load_success": "Model loaded successfully", "model_load_failed": "Model loading failed", "faiss_index_loaded": "Faiss index loaded successfully", "faiss_index_load_failed": "Faiss index loading failed", "invalid_image": "Invalid image data", "processing_error": "Error occurred during processing"}, "errors": {"param_error": "Parameter error, please refer to the API documentation", "image_decode_error": "Image decoding failed", "model_inference_error": "Model inference failed", "feature_comparison_error": "Feature comparison failed", "file_not_found": "File not found", "permission_denied": "Permission denied", "memory_error": "Insufficient memory", "timeout_error": "Processing timeout"}, "status": {"initializing": "Initializing", "ready": "Ready", "processing": "Processing", "completed": "Completed", "error": "Error", "stopped": "Stopped"}, "config": {"similarity_threshold": "Similarity <PERSON><PERSON><PERSON><PERSON>", "recognition_interval": "Recognition Interval", "max_face_size": "<PERSON> Face Si<PERSON>", "recognition_mode": "Recognition Mode", "known_face": "Known Face Recognition", "stranger_detection": "Stranger Detection", "all_mode": "All Mode"}}