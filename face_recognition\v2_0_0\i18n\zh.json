{"algorithm_name": "人脸识别算法", "algorithm_description": "基于深度学习的人脸识别与身份验证算法", "messages": {"face_detected": "检测到人脸", "face_recognized": "识别到已知人脸", "stranger_detected": "检测到陌生人", "no_face_detected": "未检测到人脸", "face_too_small": "人脸尺寸过小", "face_too_blurry": "人脸模糊度过高", "multiple_faces": "检测到多张人脸", "recognition_success": "人脸识别成功", "recognition_failed": "人脸识别失败", "feature_extraction_failed": "特征提取失败", "model_load_success": "模型加载成功", "model_load_failed": "模型加载失败", "faiss_index_loaded": "Faiss索引加载成功", "faiss_index_load_failed": "Faiss索引加载失败", "invalid_image": "无效的图像数据", "processing_error": "处理过程中发生错误"}, "errors": {"param_error": "参数错误，请参照接口文档重新传参", "image_decode_error": "图像解码失败", "model_inference_error": "模型推理失败", "feature_comparison_error": "特征比对失败", "file_not_found": "文件未找到", "permission_denied": "权限不足", "memory_error": "内存不足", "timeout_error": "处理超时"}, "status": {"initializing": "正在初始化", "ready": "就绪", "processing": "正在处理", "completed": "处理完成", "error": "错误", "stopped": "已停止"}, "config": {"similarity_threshold": "相似度阈值", "recognition_interval": "识别间隔", "max_face_size": "最大人脸尺寸", "recognition_mode": "识别模式", "known_face": "已知人脸识别", "stranger_detection": "陌生人检测", "all_mode": "全部模式"}}