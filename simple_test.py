#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的人脸识别算法测试
"""

import os
import json

def test_config():
    """测试配置文件"""
    print("测试配置文件...")
    
    config_path = "face_recognition/v2_0_0/config/documentation.json"
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        print(f"✓ 配置加载成功: {config['modelName']} v{config['modelVersion']}")
        return True
    else:
        print("✗ 配置文件不存在")
        return False

def test_weights():
    """测试权重文件"""
    print("测试权重文件...")
    
    weights_dir = "face_recognition/v2_0_0/weights"
    required_files = ["face.rknn", "retinaface_mob.rknn"]
    
    all_exist = True
    for file_name in required_files:
        file_path = os.path.join(weights_dir, file_name)
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✓ {file_name} 存在 ({size} bytes)")
        else:
            print(f"✗ {file_name} 不存在")
            all_exist = False
    
    return all_exist

def test_structure():
    """测试目录结构"""
    print("测试目录结构...")
    
    base_dir = "face_recognition/v2_0_0"
    required_dirs = ["config", "inference", "preprocess", "postprocess", "task", "utils", "weights", "i18n"]
    
    all_exist = True
    for dir_name in required_dirs:
        dir_path = os.path.join(base_dir, dir_name)
        if os.path.exists(dir_path):
            print(f"✓ {dir_name}/ 存在")
        else:
            print(f"✗ {dir_name}/ 不存在")
            all_exist = False
    
    return all_exist

def test_files():
    """测试关键文件"""
    print("测试关键文件...")
    
    key_files = [
        "face_recognition/v2_0_0/main.py",
        "face_recognition/v2_0_0/utils/face_recognition_algo.py",
        "face_recognition/v2_0_0/utils/face_recognition_core.py",
        "face_recognition/v2_0_0/utils/i18n.py",
        "face_recognition/v2_0_0/utils/log.py",
        "face_recognition/v2_0_0/preprocess/face_preprocess.py",
        "face_recognition/v2_0_0/postprocess/post_process.py",
        "face_recognition/v2_0_0/inference/model_check.py",
        "face_recognition/v2_0_0/i18n/zh.json",
        "face_recognition/v2_0_0/i18n/en.json"
    ]
    
    all_exist = True
    for file_path in key_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✓ {os.path.basename(file_path)} 存在 ({size} bytes)")
        else:
            print(f"✗ {os.path.basename(file_path)} 不存在")
            all_exist = False
    
    return all_exist

def main():
    """主测试函数"""
    print("=" * 60)
    print("人脸识别算法结构验证")
    print("=" * 60)
    
    tests = [
        ("目录结构", test_structure),
        ("关键文件", test_files),
        ("配置文件", test_config),
        ("权重文件", test_weights),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"验证结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有验证通过！人脸识别算法结构完整。")
        
        print("\n算法特性:")
        print("- ✓ 基于detect_fallperson架构设计")
        print("- ✓ 支持多种识别模式（已知人脸、陌生人检测、全部模式）")
        print("- ✓ 基于Faiss的高效人脸特征检索")
        print("- ✓ 支持RKNN模型推理")
        print("- ✓ 完整的预处理和后处理流程")
        print("- ✓ 国际化支持（中英文）")
        print("- ✓ 可配置的参数和输出格式")
        
        print("\n使用说明:")
        print("1. 确保face_info目录下有正确的Faiss索引文件")
        print("2. 根据需要调整相似度阈值和识别间隔")
        print("3. 可通过API接口进行人脸库管理")
        
        return True
    else:
        print("❌ 部分验证失败，请检查缺失的文件或目录")
        return False

if __name__ == "__main__":
    main()
